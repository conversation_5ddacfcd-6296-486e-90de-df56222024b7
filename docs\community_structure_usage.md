# 社区结构配置使用指南

## 概述

本系统支持两种不同的社区结构配置：

1. **默认结构（default）**：传统的知识分子+群众结构
2. **子社区结构（subcommunity）**：多个子社区，每个子社区有版主和成员

## 配置方法

### 1. 修改配置文件

在 `config/config.py` 中设置社区结构类型：

```python
# 社区结构配置
COMMUNITY_STRUCTURE = "default"  # 或 "subcommunity"
```

### 2. 默认结构配置

当 `COMMUNITY_STRUCTURE = "default"` 时，系统使用传统的知识分子+群众结构：

```python
# 用户配置
NUM_USERS = 100  # 创建用户数量
INTELLECTUAL_RATIO = 0.5  # 知识分子比例

# 群体间关注关系配置
REGULAR_TO_INTELLECTUAL_FOLLOW_RATIO_MIN = 0.15  # 群众关注知识分子最小比例
REGULAR_TO_INTELLECTUAL_FOLLOW_RATIO_MAX = 0.35  # 群众关注知识分子最大比例

INTELLECTUAL_TO_REGULAR_FOLLOW_RATIO_MIN = 0.03  # 知识分子关注群众最小比例
INTELLECTUAL_TO_REGULAR_FOLLOW_RATIO_MAX = 0.08  # 知识分子关注群众最大比例

INTELLECTUAL_TO_INTELLECTUAL_FOLLOW_RATIO_MIN = 0.2  # 知识分子间关注最小比例
INTELLECTUAL_TO_INTELLECTUAL_FOLLOW_RATIO_MAX = 0.4  # 知识分子间关注最大比例

# 群体内部关注关系配置
REGULAR_FOLLOW_MEAN_RATIO = 0.25  # 群众内部关注均值比例
REGULAR_FOLLOW_STD_RATIO = 0.01   # 群众内部关注标准差比例
REGULAR_FOLLOW_MAX_RATIO = 0.4    # 群众内部关注最大比例
```

### 3. 子社区结构配置

当 `COMMUNITY_STRUCTURE = "subcommunity"` 时，系统使用子社区结构：

```python
# 子社区结构配置
NUM_SUBCOMMUNITIES = 5  # 子社区数量
MODERATORS_PER_SUBCOMMUNITY = 3  # 每个子社区的版主数量
INTER_SUBCOMMUNITY_FOLLOW_RATIO = 0.05  # 子社区间关注比例
INTRA_SUBCOMMUNITY_FOLLOW_RATIO = 0.3   # 子社区内关注比例
MEMBER_TO_MODERATOR_FOLLOW_RATIO = 0.8  # 成员关注版主的比例
```

## 结构特点

### 默认结构特点

- **知识分子**：具有较高的怀疑分数和验证阈值
- **普通群众**：具有较低的怀疑分数和验证阈值
- **关注模式**：
  - 群众大量关注知识分子
  - 知识分子少量关注群众
  - 知识分子之间相互关注
  - 群众之间使用正态分布建立关注关系

### 子社区结构特点

- **版主（头领）**：每个子社区的少数领导者，具有知识分子特质
- **成员**：子社区内的普通成员
- **相似特质**：同一子社区内的用户具有相似的人格特质
- **关注模式**：
  - 成员大量关注本子社区的版主
  - 子社区内成员之间相互关注
  - 不同子社区之间联系较少

## 子社区特质分配

系统为不同子社区分配不同的人格特质倾向：

- **子社区0（保守型）**：高尽责性、低开放性
- **子社区1（开放型）**：高开放性、高外向性
- **子社区2（内向型）**：低外向性、高宜人性
- **子社区3（外向型）**：高外向性、低神经质
- **子社区4（平衡型）**：各维度均衡

## 使用示例

### 运行默认结构

```bash
# 1. 修改配置文件
# config/config.py 中设置 COMMUNITY_STRUCTURE = "default"

# 2. 运行初始化脚本
python scripts/parallel_initialize_db.py
```

### 运行子社区结构

```bash
# 1. 修改配置文件
# config/config.py 中设置 COMMUNITY_STRUCTURE = "subcommunity"

# 2. 运行初始化脚本
python scripts/parallel_initialize_db.py
```

## 输出信息

系统会在日志中显示关系建立的详细信息：

### 默认结构输出示例

```
INFO - 使用社区结构: default
INFO - 构建默认社区结构（知识分子 + 群众）
INFO - 找到 50 个知识分子，50 个普通群众
INFO - 默认结构关系建立完成 - 成功: 1250, 失败: 0
```

### 子社区结构输出示例

```
INFO - 使用社区结构: subcommunity
INFO - 构建子社区结构
INFO - 将 100 个用户分配到 5 个子社区
INFO - 每个子社区约 20 个用户
INFO - 处理 subcommunity_1: 20 个用户 (3 个版主, 17 个成员)
INFO - 子社区结构关系建立完成 - 成功: 800, 失败: 0
```

## 注意事项

1. **experiment/structure 目录**：子社区结构的实现位于此目录下
2. **配置验证**：系统会自动验证配置参数的合理性
3. **回退机制**：如果子社区模块导入失败，系统会自动回退到默认结构
4. **数据库更新**：切换结构类型后需要重新初始化数据库

## 扩展性

系统设计支持未来添加更多社区结构类型：

1. 在 `experiment/structure/` 目录下添加新的结构实现
2. 在 `RelationshipManager` 中添加新的结构处理逻辑
3. 在配置文件中添加相应的参数

这种设计使得系统可以灵活地支持各种社区结构实验需求。
