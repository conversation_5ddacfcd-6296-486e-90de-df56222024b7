***

# 认知智能体模拟系统 - 最终实现文档 (v6.0 - 任务驱动版)

## 1. 设计哲学：同步执行与模块化聚合


### 模块职责说明
*   **交互模拟器 (`simulator.py`)**: 程序的入口，负责生成用户行为并调用**统一处理引擎**：事件队列机制，在每一轮中，随机选择一个帖子，初始根据不同用户的特质，选择用户的行为：发帖，浏览，评论，回复，插入事件队列，用户按顺序执行事件，每执行一个会引发自己的更新事件加入队列【create_memory->process_agent_cognition】，同时process_agent_cognition中触发的通知，会为其他用户添加浏览事件（如果是评论，回复产生的浏览事件，多传一个参数显指示回复内容即可）
*   **统一处理引擎 (`engine.py`)**: **系统的中央调度器**。根据行为类型，以正确的顺序编排和调用其他业务模块。
*   **记忆模块 (`memory_module.py`)**: 封装所有与 `memories` 索引相关的操作，核心是将原始行为转化为结构化的记忆。记忆类型分为：浏览帖子，评论帖子，回复评论，发送帖子
*   **信念模块 (`belief_module.py`)**: 封装所有认知变化的核心逻辑，包括STM处理、信念更新和触发特质演化。
*   **智能体模块 (`agent_module.py`)**: 封装所有与 `agents` 索引相关的操作，如获取和更新智能体特质。
*   **通知模块 (`notification_module.py`)**: 处理非核心的社交交互，如发送通知。
*   **AI服务层 (`ai_service.py`)**: 封装所有对外部AI模型的API调用。

## 3. 详细任务说明：用户行为与函数调度流程
您完全正确，我对此深表歉意。之前的文档确实在最后一步整合时，忽略了对**阶段四（求证机制）**和**阶段五（自我演化）**这两个关键反馈闭环的详细流程说明。它们是智能体能够“学习”和“成长”的核心，必须在流程中清晰地体现出来。

感谢您的耐心和精确的指正。现在，我将为您提供一份最终的、包含了所有阶段的、最详尽的实现文档，确保每一个细节都得到精确的阐述。

***

# 认知智能体模拟系统 - 最终实现文档 (v7.0 - 全流程版)

## 1. 设计哲学：同步执行与模块化聚合
(本节内容不变，保持同步执行的设计理念)

## 2. 系统架构与模块划分
(本节内容不变，保持模块化的聚合结构)

## 3. 详细任务说明：用户行为与函数调度流程 (最详尽版)



---

### **核心场景: 用户B 评论了 用户A 的帖子 (`CREATE_COMMENT`)**

我们将以此最复杂的交互场景为例，详细追踪所有可能被触发的流程。

*   **核心目标**: 为评论者B创建记忆，该记忆进入其STM队列进行评估。如果评估结果触发了信念更新，则**必须**接着检查是否需要触发**求证机制**和**自我演化**。同时，通知原帖主A。
*   **触发**: `simulator` 生成一个 `CREATE_COMMENT` 动作。

#### **函数调用链:**

1.  **`simulator.run_simulation_step()`**
    *   生成 `action_payload = { user_id: 'B', ... }`。
    *   **调用 `engine.process_user_action(action_payload)`**。

2.  **`engine.process_user_action(payload)`**
    *   **步骤 2.1: 创建记忆**
        *   **调用 `memory_module.create_memory(payload)`**。
    *   **步骤 2.2: 发送通知**
        *   **调用 `notification_module.send_comment_notification(...)`**。
    *   **步骤 2.3: 处理完整的认知流程**
        *   **调用 `belief_module.process_agent_cognition(user_id='B')`**。引擎将在此等待，直到所有认知变化（包括可能的求证和演化）全部完成。

3.  **`belief_module.process_agent_cognition(user_id)`**
    *   **阶段一 & 二: STM处理与议题识别**
        *   调用 `self.get_stm_queue(user_id)` 检索短期记忆。
        *   调用 `self.find_significant_clusters(stm_queue)` 进行聚类，并返回 `significant_clusters` 列表。
    *   **阶段三: 条件化信念更新**
        *   如果 `significant_clusters` 为空，则流程结束。
        *   **如果列表不为空**，则遍历每一个“议题”簇 `cluster`:
            *   **调用 `self.update_or_create_belief_from_cluster(user_id, cluster)`**。此函数现在会：
                1.  在 `beliefs` 索引中更新或创建一个信念文档。
                2.  计算并**返回**两个值：`updated_belief` (更新后的信念对象) 和 `deltaV` (本次更新的认知冲击量)。
            *   **获取智能体当前特质**，以用于接下来的判断。
                *   **调用 `agent_module.get_agent_traits(user_id)`**，获取 `U-S` (怀疑阈值) 和 `U-V` (求证阈值)。
            *   **--- 阶段四：求证机制 (Verification Trigger) ---**
                *   **判断**: `if updated_belief.confidence < U-V:`
                *   **行动**: 如果为真，则**调用 `self.trigger_verification_action(user_id, updated_belief)`**。

            *   **--- 阶段五：自我演化与级联更新 (Agent Evolution) ---**
                *   **判断**: `if deltaV > U-S:`
                *   **行动**: 如果为真，则执行以下两个操作：
                    1.  **特质演化**: **调用 `agent_module.evolve_agent_traits(user_id, deltaV)`**，更新 `agents` 索引中该用户的核心认知特质。
                    2.  **级联更新**: **调用 `self.propagate_belief_changes(updated_belief, deltaV)`**，此函数会查找所有与 `updated_belief` 相连的信念节点，并根据边的关系（支持/反驳）将变化传播出去，对它们进行后续更新。

4.  **`engine.process_user_action(payload)` (续)**
    *   当 `belief_module` 的函数返回时，意味着所有流程（包括可能的递归求证）已结束。
    *   引擎**返回** `SUCCESS`。

---
### **求证机制的闭环反馈 (重点说明)**

`belief_module.trigger_verification_action(user_id, belief_to_verify)` 函数的内部逻辑是实现反馈闭环的关键：

1.  **分析需求**: 函数接收到需要求证的信念，例如“AI对人类是危险的”。
2.  **生成搜索行为**: 它会构造一个新的、模拟用户主动搜索的行为。例如，生成一个 `action_payload`，其 `action_type` 为 `READ_POST`，`content` 可能是通过 AI 服务生成的、与原信念相反或中立的观点帖子，如“AI如何赋能人类社会”。
3.  **形成递归调用**: 最关键的一步，它会**再次调用顶层的 `engine.process_user_action(new_action_payload)`**。

**这意味着形成了一个深度的、递归的调用栈**:
`simulator` -> `engine` -> `belief_module` -> `engine` -> `belief_module` -> ...

这个设计在同步模型中是完全可行的，它完美地模拟了“**困惑 -> 主动探索 -> 获得新信息 -> 重新评估**”的完整心智活动闭环。

---

### **其他用户行为的任务流程**

*   **`CREATE_POST`, `CREATE_REPLY`, `READ_POST`**:
    *   这些行为的调用链与 `CREATE_COMMENT` 大致相同，比如发帖也会产生记忆，但是不会有通知，而是随机推送给部分用户。
    *   它们都会为行动者创建一条记忆，并**立即触发** `belief_module.process_agent_cognition` 函数。
    *   在这个函数内部，系统将**完整地执行全部五个阶段的逻辑判断**：STM处理 -> 信念更新 -> **（检查）**求证 -> **（检查）**演化。
    *   唯一的区别是，像 `READ_POST` 这样的被动行为不会触发 `notification_module`。

## 5. 最终版 Elasticsearch 索引设计

此设计无需变更，已能完全支持包括求证和演化在内的所有同步流程。

### 5.1 `agents` - 智能体索引
| 字段名 | ES 类型 | 职责 |
| :--- | :--- | :--- |
| `user_id` | `keyword` | 唯一标识符。 |
| `ocean_personality` | `object` | 定义基础性格。 |
| `cognitive_traits` | `object` | 存储核心认知参数。 |
| `cognitive_traits.skepticism_score` | `float` | **U-S**: 在**阶段五**被读取，用于判断是否触发演化。 |
| `cognitive_traits.verification_threshold` | `float` | **U-V**: 在**阶段四**被读取，用于判断是否触发求证。 |
| `cognitive_traits.emotional_volatility` | `float` | **U-E**: 在信念更新时被读取，调节情感冲击。 |

### 5.2 `memories` - 记忆索引
| 字段名 | ES 类型 | 职责 |
| :--- | :--- | :--- |
| `memory_id` | `keyword` | 唯一标识符。 |
| `user_id` | `keyword` | 关联智能体，用于高效检索。 |
| `timestamp` | `date` | 用于排序，构成STM队列。 |
| `type` | `keyword` | 行为类型上下文。 |
| `source` | `object` | 信息来源属性。 |
| `source.credibility_score` | `half_float` | 在信念更新时聚合为证据权重。 |
| `emotional_impact` | `object` | 在信念更新时聚合为情感影响。 |
| `content` | `text` | 原始内容。 |
| `core_proposition` | `text` | STM聚类的主要依据。 |
| `proposition_embedding` | `dense_vector` | STM聚类的技术核心。 |

### 5.3 `beliefs` - 信念索引
| 字段名 | ES 类型 | 职责 |
| :--- | :--- | :--- |
| `node_id` | `keyword` | 唯一标识符。 |
| `owner_user_id` | `keyword` | 关联智能体，用于高效检索。 |
| `belief_summary` | `text` | 信念的自然语言摘要。 |
| `embedding` | `dense_vector` | 用于快速查找相似信念。 |
| `veracity_score` | `float` | 信念的真实度得分 (-1.0 到 1.0)。 |
| `confidence` | `float` | 信念的置信度，被更新，并在**阶段四**被读取。 |
| `emotional_disposition` | `object` | 信念的情感烙印，被更新。 |
| `evidence_memory_ids` | `keyword` | 提供从信念到证据的追溯链。 |
| `relation_edges` | `nested` | 定义图结构，在**阶段五**的级联更新中被读取。 |
| `relation_edges.target_node_id` | `keyword` | 边的指向。 |
| `relation_edges.relation_type` | `keyword` | 边的类型。 |
