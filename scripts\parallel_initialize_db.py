#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import random
import datetime
import sys
import os
import time
from typing import Dict, Any, List, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from collections import defaultdict

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.database import ES_CLIENT
from src.modules.ai_service import AIService
from src.modules.belief_module import BeliefModule
from src.modules.agent_module import AgentModule
from src.modules.memory_module import MemoryModule
from lib.belief_ai_data import get_emotional_disposition, get_embedding, is_data_available

# 导入配置
from config.config import Config
import config.parallel_init_config as parallel_config

# 使用配置文件中的参数
sim_config = Config.get_simulation_config()
NUM_USERS = sim_config['num_users']
MAX_WORKERS = parallel_config.get_effective_max_workers()
BATCH_SIZE = parallel_config.get_effective_batch_size()

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ParallelDatabaseInitializer")

# 索引名称
AGENTS_INDEX = "agents"
BELIEFS_INDEX = "beliefs"
POSTS_INDEX = "posts"
COMMENTS_INDEX = "comments"
MEMORIES_INDEX = "memories"

class ParallelDatabaseInitializer:
    """
    并行数据库初始化器，使用多线程并行处理用户信念图构建，
    批量上传数据库以提高性能。
    """
    def __init__(self):
        self.ai_service = AIService(
            api_key="sk-mT8uDsuxCwxMPUlwdodDOcdjzJjOYFBQQWw17LLLDPXyqVH0",
            base_url="http://47.102.193.166:8060",
            embedding_url="http://10.201.64.106:30000/v1",
            reranker_url="http://10.201.64.106:30001/v1"
        )
        self.agent_module = AgentModule()
        self.memory_module = MemoryModule(self.ai_service)
        self.belief_module = BeliefModule(self.agent_module, self.memory_module, self.ai_service)
        
        self.beliefs_data = self._load_json_data("lib/婚姻和继承/belief.json")
        self.posts_data = self._load_json_data("lib/婚姻和继承/post.json")
        self.search_data = self._load_json_data("lib/婚姻和继承/search.json")
        
        # 创建用户ID和人格特质
        self.users = self._generate_users(NUM_USERS)
        
        # 线程安全的数据存储
        self._lock = threading.Lock()
        self._pending_beliefs = []  # 待上传的信念数据
        self._pending_relations = []  # 待上传的关系数据
        
    def _load_json_data(self, file_path: str) -> List[Dict]:
        """加载JSON数据文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return json.load(file)
        except Exception as e:
            logger.error(f"加载{file_path}失败: {e}")
            return []
            
    def _generate_users(self, num_users: int) -> Dict[str, Dict[str, Any]]:
        """生成指定数量的用户，并赋予性格特质，区分普通群众和知识分子"""
        users = {}
        
        # 设置知识分子的比例（较高怀疑分数的用户）
        intellectual_ratio = sim_config['intellectual_ratio']
        num_intellectuals = max(1, int(num_users * intellectual_ratio))
        
        for i in range(1, num_users + 1):
            user_id = f"user_{i}"
            
            # 随机生成大五人格特质
            ocean = {
                "O": round(random.uniform(0.2, 0.9), 2),  # 开放性
                "C": round(random.uniform(0.2, 0.9), 2),  # 尽责性 
                "E": round(random.uniform(0.2, 0.9), 2),  # 外向性
                "A": round(random.uniform(0.2, 0.9), 2),  # 宜人性
                "N": round(random.uniform(0.2, 0.9), 2)   # 神经质
            }
            
            # 认知特质 - 根据用户类型生成
            is_intellectual = i <= num_intellectuals
            
            if is_intellectual:
                # 知识分子 - 较高的怀疑分数
                skepticism_score = round(random.uniform(0.6, 0.9), 2)
                verification_threshold = round(random.uniform(0.6, 0.9), 2)
                emotional_volatility = round(random.uniform(0.2, 0.5), 2)
            else:
                # 普通群众 - 较低的怀疑分数
                skepticism_score = round(random.uniform(0.2, 0.5), 2)
                verification_threshold = round(random.uniform(0.2, 0.5), 2)
                emotional_volatility = round(random.uniform(0.5, 0.9), 2)

            cognitive_traits = {
                "skepticism_score": skepticism_score,
                "verification_threshold": verification_threshold,
                "emotional_volatility": emotional_volatility,
                "is_intellectual": is_intellectual  # 添加标记，区分知识分子和普通群众
            }

            # 添加用户群体分类
            user_group = "intellectual" if is_intellectual else "regular"
            
            users[user_id] = {
                "user_id": user_id,
                "user_group": user_group,  # 添加用户群体分类
                "ocean_personality": ocean,
                "cognitive_traits": cognitive_traits,
                "followed_posts": [],
                "followed_users": [],
                "followers": [],
                "posts": [],
                "comments": []
            }
            
        return users
        
    def initialize_database(self):
        """并行初始化数据库，添加用户、信念和帖子"""
        logger.info("开始并行初始化数据库...")
        
        # 清空并重建索引
        self._reset_indices()
        
        # 添加用户
        self._add_users()
        
        # 建立用户之间的关注关系
        self._build_user_relationships()
        
        # 并行添加用户信念（核心改进）
        self._add_beliefs_parallel()
        
        # 添加帖子
        self._add_posts()
        
        # 打印用户信念图
        self._print_belief_graphs_for_all_users()
        
        logger.info("并行数据库初始化完成!")
        
    def _reset_indices(self):
        """重置所有索引"""
        indices = [AGENTS_INDEX, BELIEFS_INDEX, POSTS_INDEX, COMMENTS_INDEX, MEMORIES_INDEX]
        
        for index in indices:
            if ES_CLIENT.indices.exists(index=index):
                logger.info(f"删除索引: {index}")
                ES_CLIENT.indices.delete(index=index)
                
            logger.info(f"创建索引: {index}")
            
            # 根据索引类型设置不同的映射
            if index == BELIEFS_INDEX:
                mapping = {
                    "properties": {
                        "embedding": {"type": "dense_vector", "dims": 4096},
                        "relation_edges": {"type": "nested"}
                    }
                }
                ES_CLIENT.indices.create(index=index, mappings=mapping)
            elif index == MEMORIES_INDEX:
                mapping = {
                    "properties": {
                        "user_id": {"type": "keyword"},
                        "type": {"type": "keyword"},
                        "embedding": {"type": "dense_vector", "dims": 4096},
                        "timestamp": {"type": "date"}
                    }
                }
                ES_CLIENT.indices.create(index=index, mappings=mapping)
            else:
                ES_CLIENT.indices.create(index=index)
        
    def _add_users(self):
        """将生成的用户批量添加到数据库中"""
        logger.info(f"批量添加 {len(self.users)} 个用户到数据库...")

        # 统计用户类型
        intellectual_count = 0
        regular_count = 0

        # 准备批量操作
        bulk_operations = []

        for user_id, user_data in self.users.items():
            # 添加索引操作到批量操作列表
            bulk_operations.append({
                "index": {
                    "_index": AGENTS_INDEX,
                    "_id": user_id
                }
            })
            bulk_operations.append(user_data)

            # 统计用户类型
            if user_data["cognitive_traits"].get("is_intellectual", False):
                intellectual_count += 1
            else:
                regular_count += 1

        # 执行批量操作
        try:
            if bulk_operations:
                logger.info(f"开始批量插入 {len(self.users)} 个用户...")
                response = ES_CLIENT.bulk(body=bulk_operations)

                # 检查批量操作结果
                errors = []
                successful_count = 0

                for item in response["items"]:
                    if "index" in item:
                        if item["index"]["status"] in [200, 201]:
                            successful_count += 1
                        else:
                            errors.append(item["index"])

                logger.info(f"用户批量添加完成 - 成功: {successful_count} 个, 失败: {len(errors)} 个")
                logger.info(f"用户类型统计 - 知识分子: {intellectual_count} 个, 普通群众: {regular_count} 个")

                if errors:
                    logger.warning(f"有 {len(errors)} 个用户添加失败")
                    for error in errors[:5]:  # 只显示前5个错误
                        logger.warning(f"错误详情: {error}")
            else:
                logger.warning("没有用户需要添加")

        except Exception as e:
            logger.error(f"批量添加用户失败: {e}")
            # 如果批量操作失败，回退到逐个添加
            logger.info("回退到逐个添加用户...")
            successful_count = 0
            for user_id, user_data in self.users.items():
                try:
                    ES_CLIENT.index(index=AGENTS_INDEX, id=user_id, document=user_data)
                    successful_count += 1
                except Exception as individual_error:
                    logger.error(f"添加用户 {user_id} 失败: {individual_error}")

            logger.info(f"逐个添加完成 - 成功: {successful_count} 个")
            logger.info(f"用户类型统计 - 知识分子: {intellectual_count} 个, 普通群众: {regular_count} 个")

    def _build_user_relationships(self):
        """为用户之间建立关注关系"""
        # 计算知识分子数量（从实际用户数据中统计）
        num_intellectuals = sum(1 for user in self.users.values()
                               if user["cognitive_traits"].get("is_intellectual", False))

        # 调用关系建立方法
        self._build_follow_relationships(self.users, num_intellectuals)

    def _build_follow_relationships(self, users: Dict[str, Dict[str, Any]], num_intellectuals: int):
        """为用户之间建立关注关系，知识分子应该有更多粉丝"""
        logger.info("为用户之间建立关注关系...")

        # 获取知识分子和普通群众的ID
        intellectual_ids = [uid for uid, user in users.items()
                           if user["cognitive_traits"].get("is_intellectual", False)]
        regular_user_ids = [uid for uid, user in users.items()
                          if not user["cognitive_traits"].get("is_intellectual", False)]

        logger.info(f"找到 {len(intellectual_ids)} 个知识分子，{len(regular_user_ids)} 个普通群众")

        # 1. 为知识分子建立粉丝关系（普通群众关注知识分子）
        self._build_intellectual_followers(intellectual_ids, regular_user_ids)

        # 2. 知识分子关注普通群众（新增跨群体关注）
        self._build_intellectual_follow_regular(intellectual_ids, regular_user_ids)

        # 3. 普通群众之间建立关注关系
        self._build_regular_user_relationships(regular_user_ids)

        # 4. 知识分子之间建立关注关系
        self._build_intellectual_relationships(intellectual_ids)

        logger.info("所有用户关注关系建立完成")

    def _build_intellectual_followers(self, intellectual_ids: List[str], regular_user_ids: List[str]):
        """为知识分子建立粉丝关系 - 普通群众关注知识分子"""
        logger.info("为知识分子建立粉丝关系...")

        if not intellectual_ids or not regular_user_ids:
            logger.warning("没有足够的用户建立知识分子粉丝关系")
            return

        # 收集所有关注关系
        all_follow_relationships = []

        for intellectual_id in intellectual_ids:
            # 知识分子应该有较多粉丝，使用配置文件中的比例参数
            follow_ratio = random.uniform(
                sim_config['regular_to_intellectual_follow_ratio_min'],
                sim_config['regular_to_intellectual_follow_ratio_max']
            )
            num_followers = max(1, int(len(regular_user_ids) * follow_ratio))

            # 随机选择粉丝
            followers = random.sample(regular_user_ids, min(num_followers, len(regular_user_ids)))

            logger.info(f"为知识分子 {intellectual_id} 准备添加 {len(followers)} 个粉丝")

            # 添加到批量关注列表
            for follower_id in followers:
                all_follow_relationships.append((follower_id, intellectual_id))

        # 批量执行关注操作
        if all_follow_relationships:
            logger.info(f"开始批量建立知识分子粉丝关系，共 {len(all_follow_relationships)} 个关系")
            result = self.agent_module.batch_update_user_follow(all_follow_relationships, follow=True)

            logger.info(f"知识分子粉丝关系建立完成 - 成功: {result['successful_count']}, 失败: {result['failed_count']}")

            if result['failed_count'] > 0:
                logger.warning(f"有 {result['failed_count']} 个关系建立失败")
                for failed_rel in result.get('failed_relationships', []):
                    if len(failed_rel) >= 3:
                        logger.debug(f"失败关系: {failed_rel[0]} -> {failed_rel[1]}, 原因: {failed_rel[2]}")
        else:
            logger.info("没有需要建立的知识分子粉丝关系")

    def _build_intellectual_follow_regular(self, intellectual_ids: List[str], regular_user_ids: List[str]):
        """知识分子关注普通群众 - 新增跨群体关注"""
        logger.info("建立知识分子关注普通群众的关系...")

        if not intellectual_ids or not regular_user_ids:
            logger.warning("没有足够的用户建立知识分子关注普通群众的关系")
            return

        # 收集所有关注关系
        all_follow_relationships = []

        for intellectual_id in intellectual_ids:
            # 知识分子关注普通群众的比例，使用配置文件中的参数
            follow_ratio = random.uniform(
                sim_config['intellectual_to_regular_follow_ratio_min'],
                sim_config['intellectual_to_regular_follow_ratio_max']
            )
            num_to_follow = max(1, int(len(regular_user_ids) * follow_ratio))

            # 随机选择要关注的普通群众
            followees = random.sample(regular_user_ids, min(num_to_follow, len(regular_user_ids)))

            logger.info(f"知识分子 {intellectual_id} 准备关注 {len(followees)} 个普通群众")

            # 添加到批量关注列表
            for followee_id in followees:
                all_follow_relationships.append((intellectual_id, followee_id))

        # 批量建立关注关系
        if all_follow_relationships:
            logger.info(f"批量建立 {len(all_follow_relationships)} 个知识分子关注普通群众的关系...")
            result = self.agent_module.batch_update_user_follow(all_follow_relationships)

            if result and result.get('success'):
                logger.info(f"成功建立 {result.get('successful_count', 0)} 个知识分子关注普通群众的关系")
                if result.get('failed_count', 0) > 0:
                    logger.warning(f"失败 {result.get('failed_count', 0)} 个关系")
                    for failed_rel in result.get('failed_relationships', []):
                        if len(failed_rel) >= 3:
                            logger.debug(f"失败关系: {failed_rel[0]} -> {failed_rel[1]}, 原因: {failed_rel[2]}")
        else:
            logger.info("没有需要建立的知识分子关注普通群众的关系")

    def _build_regular_user_relationships(self, regular_user_ids: List[str]):
        """普通群众之间建立关注关系 - 使用正态分布分配粉丝数量"""
        logger.info("为普通群众之间建立关注关系...")

        if len(regular_user_ids) < 2:
            logger.warning("普通群众数量不足，无法建立相互关注关系")
            return

        total_users = len(regular_user_ids)
        logger.info(f"普通群众总数: {total_users}")

        # 为每个用户生成粉丝数量，使用正态分布，参数从配置文件读取
        mean_followers = total_users * sim_config['regular_follow_mean_ratio']  # 均值
        std_followers = total_users * sim_config['regular_follow_std_ratio']    # 标准差

        # 最小值为0，最大值从配置文件读取
        min_followers = 0
        max_followers = int(total_users * sim_config['regular_follow_max_ratio'])

        logger.info(f"粉丝数量分布参数 - 均值: {mean_followers:.1f}, 标准差: {std_followers:.1f}, 范围: [{min_followers}, {max_followers}]")

        # 为每个用户生成粉丝数量
        user_follower_counts = {}
        for user_id in regular_user_ids:
            # 使用正态分布生成粉丝数量
            follower_count = random.normalvariate(mean_followers, std_followers)
            # 限制在合理范围内
            follower_count = max(min_followers, min(max_followers, int(round(follower_count))))
            user_follower_counts[user_id] = follower_count

        logger.info(f"粉丝数量统计 - 最小: {min(user_follower_counts.values())}, "
                   f"最大: {max(user_follower_counts.values())}, "
                   f"平均: {sum(user_follower_counts.values()) / len(user_follower_counts):.1f}")

        # 收集所有关注关系
        all_follow_relationships = []

        # 为每个用户分配粉丝
        for user_id, target_follower_count in user_follower_counts.items():
            if target_follower_count == 0:
                continue

            # 可以成为粉丝的用户（排除自己）
            potential_followers = [uid for uid in regular_user_ids if uid != user_id]
            if not potential_followers:
                continue

            # 确保不超过可用用户数量
            actual_follower_count = min(target_follower_count, len(potential_followers))

            if actual_follower_count > 0:
                # 随机选择粉丝
                followers = random.sample(potential_followers, actual_follower_count)

                # 添加到批量关注列表（粉丝关注该用户）
                for follower_id in followers:
                    all_follow_relationships.append((follower_id, user_id))

        logger.info(f"生成的关注关系总数: {len(all_follow_relationships)}")

        # 批量执行关注操作
        if all_follow_relationships:
            logger.info(f"开始批量建立普通群众关注关系，共 {len(all_follow_relationships)} 个关系")
            result = self.agent_module.batch_update_user_follow(all_follow_relationships, follow=True)

            logger.info(f"普通群众关注关系建立完成 - 成功: {result['successful_count']}, 失败: {result['failed_count']}")

            if result['failed_count'] > 0:
                logger.warning(f"有 {result['failed_count']} 个关系建立失败")
                for failed_rel in result.get('failed_relationships', []):
                    if len(failed_rel) >= 3:
                        logger.debug(f"失败关系: {failed_rel[0]} -> {failed_rel[1]}, 原因: {failed_rel[2]}")
        else:
            logger.info("没有需要建立的普通群众关注关系")

    def _build_intellectual_relationships(self, intellectual_ids: List[str]):
        """知识分子之间建立关注关系"""
        logger.info("为知识分子之间建立关注关系...")

        if len(intellectual_ids) < 2:
            logger.warning("知识分子数量不足，无法建立相互关注关系")
            return

        # 收集所有关注关系
        all_follow_relationships = []

        # 知识分子之间有较高的相互关注概率
        for i, intellectual_id in enumerate(intellectual_ids):
            # 可被关注的其他知识分子（排除自己）
            other_intellectuals = [uid for j, uid in enumerate(intellectual_ids) if i != j]

            if not other_intellectuals:
                continue

            # 知识分子关注其他知识分子的概率，使用配置文件中的参数
            follow_ratio = random.uniform(
                sim_config['intellectual_to_intellectual_follow_ratio_min'],
                sim_config['intellectual_to_intellectual_follow_ratio_max']
            )
            num_to_follow = max(1, int(len(other_intellectuals) * follow_ratio))

            # 随机选择要关注的知识分子
            followees = random.sample(other_intellectuals, min(num_to_follow, len(other_intellectuals)))

            # 添加到批量关注列表
            for followee_id in followees:
                all_follow_relationships.append((intellectual_id, followee_id))

        # 批量执行关注操作
        if all_follow_relationships:
            logger.info(f"开始批量建立知识分子关注关系，共 {len(all_follow_relationships)} 个关系")
            result = self.agent_module.batch_update_user_follow(all_follow_relationships, follow=True)

            logger.info(f"知识分子关注关系建立完成 - 成功: {result['successful_count']}, 失败: {result['failed_count']}")

            if result['failed_count'] > 0:
                logger.warning(f"有 {result['failed_count']} 个关系建立失败")
                for failed_rel in result.get('failed_relationships', []):
                    if len(failed_rel) >= 3:
                        logger.debug(f"失败关系: {failed_rel[0]} -> {failed_rel[1]}, 原因: {failed_rel[2]}")
        else:
            logger.info("没有需要建立的知识分子关注关系")

    def _add_beliefs_parallel(self):
        """并行为每个用户添加信念，批量上传到数据库"""
        logger.info("开始并行为用户添加信念...")

        # 检查是否有预处理的AI数据
        if is_data_available():
            logger.info("✓ 检测到预处理的AI数据，将使用真实AI生成的数据")
        else:
            logger.warning("⚠ 未检测到预处理的AI数据")
            logger.warning("将使用AI服务实时生成数据，这会非常慢（预计需要2-3小时）")
            logger.info("强烈建议先运行: python scripts/preprocess_beliefs.py")

            # 询问用户是否继续
            import time
            logger.warning("如果您想继续使用实时AI服务，请等待10秒...")
            logger.warning("或者按 Ctrl+C 取消，先运行预处理脚本")
            try:
                time.sleep(10)
                logger.info("继续使用实时AI服务...")
            except KeyboardInterrupt:
                logger.info("用户取消操作，请先运行预处理脚本")
                return

        # 将用户分组进行并行处理
        user_items = list(self.users.items())
        total_users = len(user_items)

        logger.info(f"开始并行处理 {total_users} 个用户的信念图构建，使用 {MAX_WORKERS} 个线程")

        # 使用线程池并行处理用户信念
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # 提交所有用户的信念构建任务
            future_to_user = {
                executor.submit(self._process_user_beliefs, user_id, user_data): user_id
                for user_id, user_data in user_items
            }

            processed_count = 0

            # 收集结果并定期批量上传
            for future in as_completed(future_to_user):
                user_id = future_to_user[future]
                try:
                    user_beliefs_data = future.result()
                    processed_count += 1

                    # 将用户的信念数据添加到待上传队列
                    with self._lock:
                        self._pending_beliefs.extend(user_beliefs_data['beliefs'])
                        self._pending_relations.extend(user_beliefs_data['relations'])

                    # 每处理完一定数量的用户或达到批次大小时，执行批量上传
                    if processed_count % BATCH_SIZE == 0 or processed_count == total_users:
                        self._batch_upload_beliefs()

                    # 显示进度
                    if processed_count % 50 == 0 or processed_count == total_users:
                        logger.info(f"信念图构建进度: {processed_count}/{total_users} ({processed_count/total_users*100:.1f}%)")

                except Exception as e:
                    logger.error(f"处理用户 {user_id} 的信念时发生错误: {e}")

        # 确保所有剩余的数据都被上传
        self._batch_upload_beliefs()

        logger.info("并行信念添加完成!")

    def _process_user_beliefs(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, List]:
        """为单个用户处理信念图构建"""
        user_beliefs = []
        user_relations = []

        # 根据用户类型确定该用户可能持有的信念数量
        is_intellectual = user_data["cognitive_traits"].get("is_intellectual", False)
        if is_intellectual:
            # 知识分子添加信念
            num_beliefs = random.randint(sim_config['intellectual_belief_min'],
                                       sim_config['intellectual_belief_max'])
        else:
            # 群众添加信念
            num_beliefs = random.randint(sim_config['regular_belief_min'],
                                       sim_config['regular_belief_max'])

        # 如果信念数量为0，跳过该用户
        if num_beliefs == 0:
            logger.debug(f"用户 {user_id} (群众) 不添加任何信念")
            return {"beliefs": [], "relations": []}

        # 随机选择信念
        chosen_beliefs = random.sample(range(1, len(self.beliefs_data)+1), num_beliefs)

        belief_id_counter = int(time.time() * 1000000) + hash(user_id) % 1000000  # 生成唯一ID
        user_belief_ids = []

        # 为用户创建信念
        for belief_idx in chosen_beliefs:
            belief = next((b for b in self.beliefs_data if b["id"] == belief_idx), None)
            if not belief:
                continue

            belief_id = f"belief_{belief_id_counter}"
            belief_id_counter += 1
            user_belief_ids.append(belief_id)

            # 根据用户类型选择信念来源的概率
            if is_intellectual:
                # 知识分子更可能从proposition读取，但也会从其他来源读取
                source_weights = {"proposition": 0.8, "oppositional_view": 0.1, "vernacular_saying": 0.1}
            else:
                # 普通群众更可能从oppositional_view和vernacular_saying读取，但也会从proposition读取
                source_weights = {"proposition": 0.05, "oppositional_view": 0.9, "vernacular_saying": 0.05}

            # 根据权重随机选择信念来源
            sources = list(source_weights.keys())
            weights = list(source_weights.values())
            belief_source = random.choices(sources, weights=weights, k=1)[0]

            # 获取对应来源的信念内容
            belief_summary = belief[belief_source]

            # 根据用户类型和来源调整真实度和置信度
            if is_intellectual:
                # 知识分子的信念真实度和置信度通常较高，特别是从proposition来源的
                if belief_source == "proposition":
                    veracity_score = random.uniform(0.6, 1.0)  # 偏向正确
                    confidence = random.uniform(0.6, 0.9)
                else:
                    veracity_score = random.uniform(0.4, 0.9)  # 稍微偏向正确
                    confidence = random.uniform(0.5, 0.8)
            else:
                # 普通群众的信念真实度和置信度可能更加多样化
                if belief_source == "proposition":
                    veracity_score = random.uniform(0.3, 0.8)  # 更加多样化
                    confidence = random.uniform(0.3, 0.7)
                else:
                    veracity_score = random.uniform(0.0, 0.7)  # 可能包含更多错误信息
                    confidence = random.uniform(0.4, 0.9)

            # 获取AI数据
            try:
                if is_data_available():
                    # 使用预处理的AI数据
                    emotional_disposition = get_emotional_disposition(belief_idx, belief_source)
                    embedding = get_embedding(belief_idx, belief_source)
                else:
                    # 使用AI服务实时生成
                    emotional_disposition, embedding = self._get_ai_data_for_belief(belief_summary)
            except Exception as e:
                logger.error(f"获取信念 {belief_id} 的AI数据失败: {e}")
                # 使用默认值
                emotional_disposition = {
                    "joy": 0.5, "sadness": 0.5, "anger": 0.5, "fear": 0.5,
                    "surprise": 0.5, "trust": 0.5, "disgust": 0.5, "anticipation": 0.5
                }
                embedding = [0.0] * 4096  # 默认嵌入向量

            # 创建符合BeliefModule期望结构的信念文档
            belief_doc = {
                "node_id": belief_id,
                "owner_user_id": user_id,
                "belief_summary": belief_summary,
                "embedding": embedding,
                "veracity_score": veracity_score,
                "confidence": confidence,
                "emotional_disposition": emotional_disposition,
                "evidence_memory_ids": [],
                "relation_edges": []
            }

            user_beliefs.append(belief_doc)

        # 为该用户的信念建立关系（在内存中）
        if len(user_belief_ids) > 1:
            relations = self._build_belief_relations_in_memory(user_beliefs)
            user_relations.extend(relations)

        logger.debug(f"为用户 {user_id} 构建了 {len(user_beliefs)} 个信念和 {len(user_relations)} 个关系")

        return {"beliefs": user_beliefs, "relations": user_relations}

    def _build_belief_relations_in_memory(self, user_beliefs: List[Dict]) -> List[Dict]:
        """在内存中为用户的信念建立关系，不直接写入数据库"""
        relations = []

        # 为每个信念建立与其他信念的关系
        for i, belief1 in enumerate(user_beliefs):
            for j, belief2 in enumerate(user_beliefs):
                if i == j:
                    continue

                # 尝试使用AI服务确定两个信念之间的关系
                try:
                    relation_info = None
                    try:
                        # 尝试使用AI服务判断关系
                        relation_info = self.ai_service.determine_detailed_belief_relationship(
                            belief1["belief_summary"], belief2["belief_summary"])
                        logger.debug(f"成功获取信念关系: {relation_info}")

                        # 检查AI服务返回的数据类型
                        if not isinstance(relation_info, dict):
                            logger.warning(f"AI服务返回了非字典类型: {type(relation_info)}, 使用随机值")
                            raise ValueError("AI服务返回格式错误")

                    except Exception as e:
                        logger.warning(f"获取信念关系失败，使用随机值: {e}")
                        # AI服务失败时的备选方案
                        relation_category = random.choice(["支持", "反驳", "无关"])
                        if relation_category == "支持":
                            relation_type = random.choice(["蕴含/前提", "因果支持", "证据支持", "相关"])
                            weight = round(random.uniform(0.5, 1.0), 2)
                        elif relation_category == "反驳":
                            relation_type = random.choice(["排斥", "削弱", "反证"])
                            weight = round(random.uniform(0.5, 1.0), 2)
                        else: # 无关
                            relation_type = None
                            weight = 0.0

                        relation_info = {
                            "relation_category": relation_category,
                            "relation_type": relation_type,
                            "weight": weight,
                            "confidence": round(random.uniform(0.5, 1.0), 2),
                            "explanation": "随机生成的关系"
                        }

                    # 确保relation_info是字典类型
                    if not isinstance(relation_info, dict):
                        logger.warning(f"relation_info不是字典类型，跳过: {relation_info}")
                        continue

                    # 如果关系为"无关"或类型为None，则不创建关系边
                    if relation_info.get("relation_category") == "无关" or not relation_info.get("relation_type"):
                        logger.debug(f"信念 {belief1['node_id']} 和 {belief2['node_id']} 无关，跳过创建关系。")
                        continue

                    # 确保使用正确的字段名
                    relation_type = relation_info.get("relation_type")
                    relation_strength = relation_info.get("weight")

                    # 验证必要字段
                    if not relation_type or relation_strength is None:
                        logger.warning(f"关系信息不完整，跳过: {relation_info}")
                        continue

                    # 创建关系边
                    new_edge = {
                        "target_node_id": belief2["node_id"],
                        "relation_type": relation_type,
                        "relation_strength": relation_strength
                    }

                    # 将关系添加到belief1的relation_edges中
                    if "relation_edges" not in belief1:
                        belief1["relation_edges"] = []
                    belief1["relation_edges"].append(new_edge)

                    # 记录关系更新，用于后续批量上传
                    relations.append({
                        "belief_id": belief1["node_id"],
                        "relation_edges": belief1["relation_edges"]
                    })

                except Exception as e:
                    logger.error(f"为信念 {belief1['node_id']} 和 {belief2['node_id']} 建立关系失败: {e}")

        return relations

    def _batch_upload_beliefs(self):
        """批量上传待处理的信念数据到数据库"""
        with self._lock:
            if not self._pending_beliefs:
                return

            beliefs_to_upload = self._pending_beliefs.copy()
            relations_to_upload = self._pending_relations.copy()
            self._pending_beliefs.clear()
            self._pending_relations.clear()

        if not beliefs_to_upload:
            return

        logger.info(f"开始批量上传 {len(beliefs_to_upload)} 个信念到数据库...")

        # 准备批量操作
        bulk_operations = []

        for belief_doc in beliefs_to_upload:
            bulk_operations.append({
                "index": {
                    "_index": BELIEFS_INDEX,
                    "_id": belief_doc["node_id"]
                }
            })
            bulk_operations.append(belief_doc)

        # 执行批量上传
        try:
            if bulk_operations:
                response = ES_CLIENT.bulk(body=bulk_operations)

                # 检查批量操作结果
                errors = []
                successful_count = 0

                for item in response["items"]:
                    if "index" in item:
                        if item["index"]["status"] in [200, 201]:
                            successful_count += 1
                        else:
                            errors.append(item["index"])

                logger.info(f"信念批量上传完成 - 成功: {successful_count} 个, 失败: {len(errors)} 个")

                if errors:
                    logger.warning(f"有 {len(errors)} 个信念上传失败")
                    for error in errors[:5]:  # 只显示前5个错误
                        logger.warning(f"错误详情: {error}")

        except Exception as e:
            logger.error(f"批量上传信念失败: {e}")
            # 如果批量操作失败，回退到逐个添加
            logger.info("回退到逐个上传信念...")
            successful_count = 0
            for belief_doc in beliefs_to_upload:
                try:
                    ES_CLIENT.index(index=BELIEFS_INDEX, id=belief_doc["node_id"], document=belief_doc)
                    successful_count += 1
                except Exception as individual_error:
                    logger.error(f"上传信念 {belief_doc['node_id']} 失败: {individual_error}")

            logger.info(f"逐个上传完成 - 成功: {successful_count} 个")

        # 批量更新关系
        if relations_to_upload:
            self._batch_update_relations(relations_to_upload)

    def _batch_update_relations(self, relations_to_upload: List[Dict]):
        """批量更新信念关系"""
        logger.info(f"开始批量更新 {len(relations_to_upload)} 个信念关系...")

        # 准备批量更新操作
        bulk_operations = []

        for relation_update in relations_to_upload:
            bulk_operations.append({
                "update": {
                    "_index": BELIEFS_INDEX,
                    "_id": relation_update["belief_id"]
                }
            })
            bulk_operations.append({
                "doc": {"relation_edges": relation_update["relation_edges"]}
            })

        # 执行批量更新
        try:
            if bulk_operations:
                response = ES_CLIENT.bulk(body=bulk_operations)

                # 检查批量操作结果
                errors = []
                successful_count = 0

                for item in response["items"]:
                    if "update" in item:
                        if item["update"]["status"] in [200, 201]:
                            successful_count += 1
                        else:
                            errors.append(item["update"])

                logger.info(f"关系批量更新完成 - 成功: {successful_count} 个, 失败: {len(errors)} 个")

                if errors:
                    logger.warning(f"有 {len(errors)} 个关系更新失败")
                    for error in errors[:5]:  # 只显示前5个错误
                        logger.warning(f"错误详情: {error}")

        except Exception as e:
            logger.error(f"批量更新关系失败: {e}")
            # 如果批量操作失败，回退到逐个更新
            logger.info("回退到逐个更新关系...")
            successful_count = 0
            for relation_update in relations_to_upload:
                try:
                    ES_CLIENT.update(
                        index=BELIEFS_INDEX,
                        id=relation_update["belief_id"],
                        doc={"relation_edges": relation_update["relation_edges"]}
                    )
                    successful_count += 1
                except Exception as individual_error:
                    logger.error(f"更新信念 {relation_update['belief_id']} 关系失败: {individual_error}")

            logger.info(f"逐个更新完成 - 成功: {successful_count} 个")

    def _get_ai_data_for_belief(self, belief_text: str) -> tuple:
        """使用AI服务为信念文本生成情感倾向和嵌入向量"""
        # 获取情感倾向
        try:
            emotional_disposition = self.ai_service.analyze_emotional_impact(belief_text)
            logger.debug(f"成功获取信念'{belief_text[:30]}...'的情感向量")

            # 确保所有必需的情感维度都存在，如果缺失则使用默认值
            required_emotions = ["joy", "sadness", "anger", "fear", "surprise", "trust", "disgust", "anticipation"]
            for emotion in required_emotions:
                if emotion not in emotional_disposition:
                    emotional_disposition[emotion] = 0.5
                # 确保值在0-1范围内
                emotional_disposition[emotion] = max(0.0, min(1.0, float(emotional_disposition[emotion])))
                emotional_disposition[emotion] = round(emotional_disposition[emotion], 2)

        except Exception as e:
            logger.error(f"AI服务获取情感向量失败: {e}")
            raise Exception(f"无法获取情感向量，AI服务调用失败: {e}")

        # 获取嵌入向量
        try:
            embedding = self.ai_service.get_embedding(belief_text)
            logger.debug(f"成功获取信念'{belief_text[:30]}...'的嵌入向量")
        except Exception as e:
            logger.error(f"AI服务获取嵌入向量失败: {e}")
            raise Exception(f"无法获取嵌入向量，AI服务调用失败: {e}")

        return emotional_disposition, embedding

    def _add_posts(self):
        """添加帖子到数据库"""
        logger.info("开始添加帖子...")

        post_id_counter = 1
        for post_data in self.posts_data:
            # 随机选择一个用户作为发帖人
            author_id = random.choice(list(self.users.keys()))

            post_id = f"post_{post_id_counter}"
            post_id_counter += 1

            post_doc = {
                "post_id": post_id,
                "title": post_data["title"],
                "author_id": author_id,
                "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat(),
                "content": post_data["content"],
                "comments": [],
                "followers": [],
                "views_count": random.randint(10, 1000),
                "comments_count": 0
            }

            # 添加到ES
            ES_CLIENT.index(index=POSTS_INDEX, id=post_id, document=post_doc)

            # 更新用户的帖子列表
            self.users[author_id]["posts"].append(post_id)
            ES_CLIENT.update(
                index=AGENTS_INDEX,
                id=author_id,
                doc={"posts": self.users[author_id]["posts"]}
            )

            # 为每个帖子添加1-5条评论
            num_comments = random.randint(1, 5)
            for _ in range(num_comments):
                # 随机选择一个不是作者的用户发表评论
                available_users = [uid for uid in self.users.keys() if uid != author_id]
                if not available_users:
                    continue

                commenter_id = random.choice(available_users)

                # 使用AI服务生成评论内容
                try:
                    # 获取用户特质信息
                    user_traits = self.users[commenter_id]

                    # 获取评论者的一个随机信念ID
                    commenter_beliefs = self.belief_module.get_agent_beliefs(commenter_id)
                    belief_id = random.choice(commenter_beliefs).get('node_id', 'default_belief_id') if commenter_beliefs else 'default_belief_id'

                    # 尝试通过AI服务生成评论内容
                    comment_content = self.ai_service.generate_comment_content(
                        user_id=commenter_id,
                        start_belief_id=belief_id,
                        post_content=post_data["content"],
                        user_traits=user_traits
                    )
                    logger.debug("成功通过AI生成评论内容")
                except Exception as e:
                    logger.warning(f"生成评论内容失败，使用备选内容: {e}")
                    # 备选方案：使用search.json中的观点
                    if self.search_data:
                        random_search = random.choice(self.search_data)
                        comment_content = random_search["detailed_statement"]
                    else:
                        comment_content = f"这是对帖子《{post_data['title']}》的一条评论。"

                comment_id = f"comment_{post_id}_{_+1}"

                comment_doc = {
                    "comment_id": comment_id,
                    "author_id": commenter_id,
                    "post_id": post_id,
                    "parent_comment_id": None,  # 直接评论帖子
                    "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat(),
                    "content": comment_content
                }

                # 添加到ES
                ES_CLIENT.index(index=COMMENTS_INDEX, id=comment_id, document=comment_doc)

                # 更新帖子的评论列表
                post_doc["comments"].append(comment_id)
                post_doc["comments_count"] += 1

                # 更新用户的评论列表
                self.users[commenter_id]["comments"].append(comment_id)
                ES_CLIENT.update(
                    index=AGENTS_INDEX,
                    id=commenter_id,
                    doc={"comments": self.users[commenter_id]["comments"]}
                )

            # 更新帖子的评论信息
            ES_CLIENT.update(
                index=POSTS_INDEX,
                id=post_id,
                doc={
                    "comments": post_doc["comments"],
                    "comments_count": post_doc["comments_count"]
                }
            )

        logger.info(f"成功添加了 {post_id_counter-1} 个帖子")

    def _print_belief_graphs_for_all_users(self):
        """打印所有用户的信念图"""
        logger.info("开始打印所有用户的信念图...")

        # 创建打印工具实例
        try:
            from scripts.print_belief_graph import BeliefGraphPrinter
            printer = BeliefGraphPrinter()

            # 获取所有用户ID
            user_ids = list(self.users.keys())
            if not user_ids:
                logger.info("没有找到任何用户")
                return

            logger.info(f"找到 {len(user_ids)} 个用户")

            # 为每个用户打印信念图
            for user_id in user_ids:
                printer.print_user_belief_graph(user_id)
        except ImportError as e:
            logger.warning(f"无法导入信念图打印工具: {e}")
            logger.info("跳过信念图打印步骤")


# 从独立的打印脚本导入打印工具
def main():
    """主函数"""
    logger.info("开始并行数据库初始化")

    try:
        start_time = time.time()
        initializer = ParallelDatabaseInitializer()
        initializer.initialize_database()
        end_time = time.time()

        logger.info(f"并行数据库初始化完成! 总耗时: {end_time - start_time:.2f} 秒")
        return 0
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
