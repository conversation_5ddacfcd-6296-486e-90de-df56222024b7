# config/parallel_init_config.py

import multiprocessing

# 并行处理配置
MAX_WORKERS = min(8, multiprocessing.cpu_count())  # 最大工作线程数
BATCH_SIZE = 500  # 批量上传大小

# 内存优化配置
MEMORY_OPTIMIZED = False  # 是否启用内存优化模式
MEMORY_OPTIMIZED_BATCH_SIZE = 50  # 内存优化模式下的批量大小

def get_effective_batch_size():
    """获取有效的批量大小"""
    if MEMORY_OPTIMIZED:
        return MEMORY_OPTIMIZED_BATCH_SIZE
    return BATCH_SIZE

def get_effective_max_workers():
    """获取有效的最大工作线程数"""
    max_allowed = multiprocessing.cpu_count() * 2
    return min(MAX_WORKERS, max_allowed)
