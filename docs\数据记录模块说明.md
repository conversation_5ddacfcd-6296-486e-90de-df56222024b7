# 数据记录模块说明

## 概述

数据记录模块是虚拟社区模拟系统的重要组成部分，包含用户记录模块和实验数据记录模块。这些模块用于记录和分析模拟过程中的各种状态变化，为实验验证和研究分析提供全面的数据支持。

## 用户记录模块

用户记录模块按照与信念记录和内容记录相同的逻辑，在每轮模拟前后记录用户信息，并生成详细的统计报告。

## 功能特性

### 1. 用户状态记录

- **记录时机**: 每轮模拟开始前（before）和结束后（after）
- **记录内容**: 用户的完整资料，包括：
  - 用户ID
  - 大五人格特质（OCEAN）
  - 认知特质（怀疑主义、验证阈值、情绪波动性等）
  - 社交关系（关注的用户、粉丝、关注的帖子）
  - 活动记录（发布的帖子、评论）

### 2. 变化统计分析

- **新用户检测**: 识别每轮新增的用户
- **特质变化分析**: 跟踪用户认知特质的变化
- **社交关系变化**: 监控关注关系、粉丝数量等变化
- **活动变化**: 统计新发布的帖子和评论

### 3. 报告生成

- **轮次报告**: 每轮生成详细的用户变化统计
- **总体报告**: 汇总所有轮次的用户变化趋势

## 文件输出

### 每轮输出文件

1. **`round_N_before_users.json`**: 第N轮开始前的所有用户状态
2. **`round_N_after_users.json`**: 第N轮结束后的所有用户状态
3. **`round_N_users_summary.json`**: 第N轮的用户变化统计摘要

### 总体报告文件

- **`final_users_report.json`**: 所有轮次的用户变化总体统计报告

## 统计指标

### 用户数量统计
- 总用户数（轮次前后）
- 新增用户数量
- 发生变化的用户数量

### 认知特质变化统计
- 怀疑主义得分变化次数和平均变化幅度
- 验证阈值变化次数和平均变化幅度
- 情绪波动性变化次数和平均变化幅度

### 社交关系变化统计
- 新增粉丝总数
- 新关注用户总数
- 新关注帖子总数
- 新发布帖子总数
- 新发布评论总数

## 使用方法

用户记录功能已集成到主模拟程序中，无需额外配置。运行模拟时会自动：

1. 在每轮开始前记录用户状态
2. 在每轮结束后记录用户状态
3. 生成轮次统计摘要
4. 在所有轮次完成后生成总体报告

## 日志输出示例

```
2025-08-10 13:32:01,941 - Main - INFO - 已记录轮次 1 before 阶段的所有用户信息到 round_1_before_users.json，共 5 个用户
2025-08-10 13:32:01,949 - Main - INFO - 已记录轮次 1 after 阶段的所有用户信息到 round_1_after_users.json，共 5 个用户
2025-08-10 13:32:01,949 - Main - INFO - 已生成第 1 轮模拟的用户统计信息: round_1_users_summary.json
2025-08-10 13:32:01,950 - Main - INFO - 已生成用户总体统计报告: final_users_report.json
```

## 数据结构示例

### 用户状态记录格式
```json
{
  "user_303": {
    "user_id": "user_303",
    "ocean_personality": {
      "O": 0.75,
      "C": 0.68,
      "E": 0.42,
      "A": 0.59,
      "N": 0.33
    },
    "cognitive_traits": {
      "skepticism_score": 0.72,
      "verification_threshold": 0.65,
      "emotional_volatility": 0.28
    },
    "followed_posts": [],
    "followed_users": ["user_314", "user_327"],
    "followers": ["user_317"],
    "posts": ["post_123", "post_456"],
    "comments": ["comment_789"]
  }
}
```

### 统计摘要格式
```json
{
  "round": 1,
  "new_users": [],
  "updated_users": [],
  "user_stats": {
    "total_before": 5,
    "total_after": 5,
    "new_users_count": 0,
    "updated_users_count": 0,
    "trait_changes": {
      "skepticism_changes": 0,
      "verification_threshold_changes": 0,
      "emotional_volatility_changes": 0,
      "avg_skepticism_change": 0.0,
      "avg_verification_threshold_change": 0.0,
      "avg_emotional_volatility_change": 0.0
    },
    "social_changes": {
      "new_followers_total": 0,
      "new_followed_users_total": 0,
      "new_followed_posts_total": 0,
      "new_posts_total": 0,
      "new_comments_total": 0
    }
  }
}
```

## 注意事项

1. **存储空间**: 用户记录会占用一定的存储空间，特别是在用户数量较多时
2. **性能影响**: 记录过程可能会稍微增加每轮模拟的时间
3. **数据一致性**: 确保在记录期间数据库状态稳定，避免并发修改

## 扩展性

该模块设计具有良好的扩展性，可以轻松添加：
- 更多用户属性的跟踪
- 自定义统计指标
- 不同的报告格式
- 数据可视化功能

## 实验数据记录模块

实验数据记录模块专门用于记录实验方案中需要的关键指标，支持谣言传播、回音室效应、极化现象等实验的数据收集和分析。

### 功能特性

#### 1. SIR模型指标记录

- **感染曲线**: 追踪持有谣言信念的智能体数量变化
- **康复曲线**: 追踪接受辟谣的智能体数量变化
- **易感者统计**: 记录没有相关信念的用户数量
- **详细分类**: 提供每类用户的详细信息和信念内容

#### 2. 极化指数计算

- **OEI指数**: Opinion Extremity Index = (|veracity_score| ≥ 0.5 的用户数) / 总用户数
- **极端观点用户**: 统计持有强烈观点的用户
- **温和观点用户**: 统计持有温和观点的用户
- **观点分布**: 分析社区观点的极化程度

#### 3. 智能体演化追踪

- **认知特质变化**: 跟踪怀疑主义、验证阈值、情绪波动性的变化
- **信念统计**: 记录信念数量、平均真实度、平均置信度
- **社交统计**: 追踪粉丝数、关注数、发帖数、评论数变化
- **长期追踪**: 支持对特定用户的长期演化追踪

#### 4. 网络传播分析

- **传播覆盖**: 分析内容在网络中的传播范围
- **活跃度统计**: 记录活跃用户、内容创作者、评论者数量
- **关键词追踪**: 支持特定关键词内容的传播分析
- **传播链分析**: 分析内容的传播路径和影响力

### 实验指标文件

#### 每轮输出文件

1. **`round_N_[before|after]_sir_metrics.json`**: SIR模型指标
2. **`round_N_[before|after]_polarization_metrics.json`**: 极化指数和观点分布
3. **`round_N_[before|after]_agent_evolution.json`**: 智能体演化数据
4. **`round_N_[before|after]_network_propagation.json`**: 网络传播分析

#### 总体报告文件

- **`experiment_summary.json`**: 实验数据总体摘要，包含所有指标的趋势分析

### 实验指标说明

#### SIR模型指标
```json
{
  "susceptible": {
    "count": 800,
    "ratio": 0.8
  },
  "infected": {
    "count": 150,
    "ratio": 0.15,
    "believers": [...]
  },
  "recovered": {
    "count": 50,
    "ratio": 0.05,
    "users": [...]
  }
}
```

#### 极化指数
```json
{
  "oei": 0.619,
  "extreme_opinion": {
    "count": 619,
    "ratio": 0.619,
    "users": [...]
  },
  "moderate_opinion": {
    "count": 281,
    "ratio": 0.281,
    "users": [...]
  }
}
```

#### 智能体演化
```json
{
  "tracked_users": {
    "user_123": {
      "cognitive_traits": {
        "skepticism_score": 0.75,
        "verification_threshold": 0.68,
        "emotional_volatility": 0.32
      },
      "belief_statistics": {
        "belief_count": 8,
        "avg_veracity_score": 0.42,
        "avg_confidence": 0.67
      },
      "social_statistics": {
        "followers_count": 15,
        "followed_users_count": 23,
        "posts_count": 5,
        "comments_count": 12
      }
    }
  }
}
```

### 使用配置

实验数据记录功能已集成到主模拟程序中，可以通过以下参数配置：

- **rumor_keywords**: 谣言关键词列表，用于SIR模型分析
- **content_keywords**: 内容关键词列表，用于网络传播分析
- **tracked_user_ids**: 要重点追踪的用户ID列表

### 实验验证支持

该模块专门支持实验方案中的各种验证需求：

1. **经典谣言传播SIR模型复现**: 提供感染曲线和康复曲线数据
2. **回音室效应验证**: 通过极化指数分析不同社群的观点固化程度
3. **极化现象研究**: 使用OEI指标量化社区极化程度
4. **智能体自我演化验证**: 追踪认知特质的长期变化轨迹
5. **网络结构影响分析**: 分析不同网络拓扑对传播的影响
6. **话题领域效应研究**: 比较不同领域内容的传播效果

### 性能优化

- **批量处理**: 支持大规模用户数据的高效处理
- **增量计算**: 避免重复计算，提高处理效率
- **内存管理**: 合理管理内存使用，支持长期运行
- **错误处理**: 完善的异常处理机制，确保数据完整性

### 扩展性

实验数据记录模块具有良好的扩展性：
- 可以轻松添加新的实验指标
- 支持自定义分析算法
- 可以集成外部分析工具
- 支持不同的数据输出格式
