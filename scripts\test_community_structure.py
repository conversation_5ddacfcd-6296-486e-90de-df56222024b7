#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
社区结构测试脚本

用于测试不同社区结构的配置和功能
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import Config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CommunityStructureTest")

def create_test_users(num_users: int = 20):
    """创建测试用户"""
    users = {}
    
    # 创建一些知识分子和普通群众
    for i in range(1, num_users + 1):
        user_id = f"test_user_{i}"
        is_intellectual = i <= num_users // 4  # 25%为知识分子
        
        users[user_id] = {
            "user_id": user_id,
            "user_group": "intellectual" if is_intellectual else "regular",
            "ocean_personality": {
                "O": 0.5, "C": 0.5, "E": 0.5, "A": 0.5, "N": 0.5
            },
            "cognitive_traits": {
                "skepticism_score": 0.7 if is_intellectual else 0.3,
                "verification_threshold": 0.7 if is_intellectual else 0.3,
                "emotional_volatility": 0.3 if is_intellectual else 0.7,
                "is_intellectual": is_intellectual
            },
            "followed_posts": [],
            "followed_users": [],
            "followers": [],
            "posts": [],
            "comments": []
        }
    
    return users

def test_default_structure():
    """测试默认社区结构"""
    logger.info("=" * 50)
    logger.info("测试默认社区结构")
    logger.info("=" * 50)

    # 获取配置
    config = Config.get_simulation_config()
    logger.info(f"默认结构配置: community_structure = {config.get('community_structure', 'default')}")

    # 创建测试用户
    users = create_test_users(20)

    # 统计用户类型
    intellectuals = [u for u in users.values() if u['cognitive_traits']['is_intellectual']]
    regulars = [u for u in users.values() if not u['cognitive_traits']['is_intellectual']]

    logger.info(f"创建了 {len(users)} 个测试用户:")
    logger.info(f"  - 知识分子: {len(intellectuals)} 个")
    logger.info(f"  - 普通群众: {len(regulars)} 个")

    # 模拟关系建立逻辑（不实际调用数据库）
    total_relationships = 0

    # 模拟群众关注知识分子
    for regular in regulars:
        follow_count = int(len(intellectuals) * 0.25)  # 25%的知识分子
        total_relationships += follow_count

    # 模拟知识分子关注群众
    for intellectual in intellectuals:
        follow_count = int(len(regulars) * 0.05)  # 5%的群众
        total_relationships += follow_count

    # 模拟群众之间关注
    for regular in regulars:
        follow_count = int(len(regulars) * 0.1)  # 10%的其他群众
        total_relationships += follow_count

    # 模拟知识分子之间关注
    for intellectual in intellectuals:
        follow_count = int(len(intellectuals) * 0.3)  # 30%的其他知识分子
        total_relationships += follow_count

    result = {
        'total_relationships': total_relationships,
        'structure_type': 'default'
    }

    logger.info(f"默认结构模拟结果: 预计生成 {total_relationships} 个关注关系")
    return result

def test_subcommunity_structure():
    """测试子社区结构"""
    logger.info("=" * 50)
    logger.info("测试子社区结构")
    logger.info("=" * 50)

    # 获取配置
    config = Config.get_simulation_config()
    subcommunity_config = config.get('subcommunity_config', {})
    logger.info(f"子社区结构配置: {subcommunity_config}")

    # 创建测试用户
    users = create_test_users(20)

    # 模拟子社区分配
    num_subcommunities = subcommunity_config.get('num_subcommunities', 5)
    moderators_per_subcommunity = subcommunity_config.get('moderators_per_subcommunity', 3)
    users_per_subcommunity = len(users) // num_subcommunities

    logger.info(f"将 {len(users)} 个用户分配到 {num_subcommunities} 个子社区")
    logger.info(f"每个子社区约 {users_per_subcommunity} 个用户")

    # 模拟分配过程
    user_list = list(users.items())
    total_relationships = 0

    for i in range(num_subcommunities):
        start_idx = i * users_per_subcommunity
        if i == num_subcommunities - 1:
            end_idx = len(user_list)
        else:
            end_idx = (i + 1) * users_per_subcommunity

        subcommunity_users = user_list[start_idx:end_idx]
        moderator_count = min(moderators_per_subcommunity, len(subcommunity_users))

        logger.info(f"子社区 {i+1}: {len(subcommunity_users)} 个用户 ({moderator_count} 个版主)")

        # 模拟关系计算
        # 成员关注版主
        member_count = len(subcommunity_users) - moderator_count
        total_relationships += member_count * moderator_count * 0.8  # 80%关注率

        # 子社区内部关注
        total_relationships += len(subcommunity_users) * len(subcommunity_users) * 0.3  # 30%关注率

    # 子社区间关注
    total_relationships += num_subcommunities * moderators_per_subcommunity * 0.05  # 5%跨社区关注

    result = {
        'total_relationships': int(total_relationships),
        'structure_type': 'subcommunity',
        'num_subcommunities': num_subcommunities,
        'users_per_subcommunity': users_per_subcommunity
    }

    logger.info(f"子社区结构模拟结果: 预计生成 {int(total_relationships)} 个关注关系")
    return result

def test_configuration_validation():
    """测试配置验证"""
    logger.info("=" * 50)
    logger.info("测试配置验证")
    logger.info("=" * 50)
    
    # 测试默认配置
    config = Config.get_simulation_config()
    logger.info(f"当前社区结构配置: {config.get('community_structure', 'default')}")
    logger.info(f"子社区配置: {config.get('subcommunity_config', {})}")
    
    # 验证必要的配置项
    required_default_configs = [
        'regular_to_intellectual_follow_ratio_min',
        'regular_to_intellectual_follow_ratio_max',
        'intellectual_to_regular_follow_ratio_min',
        'intellectual_to_regular_follow_ratio_max',
        'intellectual_to_intellectual_follow_ratio_min',
        'intellectual_to_intellectual_follow_ratio_max',
        'regular_follow_mean_ratio',
        'regular_follow_std_ratio',
        'regular_follow_max_ratio'
    ]
    
    missing_configs = []
    for config_key in required_default_configs:
        if config_key not in config:
            missing_configs.append(config_key)
    
    if missing_configs:
        logger.warning(f"缺少默认结构配置项: {missing_configs}")
    else:
        logger.info("✓ 默认结构配置项完整")
    
    # 验证子社区配置
    subcommunity_config = config.get('subcommunity_config', {})
    required_subcommunity_configs = [
        'num_subcommunities',
        'moderators_per_subcommunity',
        'inter_subcommunity_follow_ratio',
        'intra_subcommunity_follow_ratio',
        'member_to_moderator_follow_ratio'
    ]
    
    missing_subcommunity_configs = []
    for config_key in required_subcommunity_configs:
        if config_key not in subcommunity_config:
            missing_subcommunity_configs.append(config_key)
    
    if missing_subcommunity_configs:
        logger.warning(f"缺少子社区配置项: {missing_subcommunity_configs}")
    else:
        logger.info("✓ 子社区配置项完整")

def main():
    """主函数"""
    logger.info("开始社区结构测试")
    
    try:
        # 测试配置验证
        test_configuration_validation()
        
        # 测试默认结构
        default_result = test_default_structure()
        
        # 测试子社区结构
        subcommunity_result = test_subcommunity_structure()
        
        logger.info("=" * 50)
        logger.info("测试总结")
        logger.info("=" * 50)
        logger.info("✓ 所有测试完成")
        logger.info(f"默认结构关系数量: {default_result.get('total_relationships', 0)}")
        logger.info(f"子社区结构关系数量: {subcommunity_result.get('total_relationships', 0)}")
        
        return 0
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
