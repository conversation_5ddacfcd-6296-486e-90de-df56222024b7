#!/usr/bin/env python
# -*- coding: utf-8 -*-

import random
import logging
from typing import Dict, Any, List, Tuple
from collections import defaultdict

logger = logging.getLogger(__name__)

class SubcommunityStructure:
    """
    子社区结构实现
    
    社区分为若干个子社区，每个子社区内有：
    - 少数版主（头领）
    - 大量普通成员
    - 子社区内成员有相似特质
    - 不同子社区之间联系较少
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化子社区结构
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.num_subcommunities = config.get('num_subcommunities', 5)
        self.moderators_per_subcommunity = config.get('moderators_per_subcommunity', 3)
        self.inter_subcommunity_follow_ratio = config.get('inter_subcommunity_follow_ratio', 0.05)
        self.intra_subcommunity_follow_ratio = config.get('intra_subcommunity_follow_ratio', 0.3)
        self.member_to_moderator_follow_ratio = config.get('member_to_moderator_follow_ratio', 0.8)
        
    def assign_users_to_subcommunities(self, users: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        将用户分配到不同的子社区
        
        Args:
            users: 用户字典
            
        Returns:
            更新后的用户字典，包含子社区信息
        """
        user_list = list(users.items())
        total_users = len(user_list)
        users_per_subcommunity = total_users // self.num_subcommunities
        
        logger.info(f"将 {total_users} 个用户分配到 {self.num_subcommunities} 个子社区")
        logger.info(f"每个子社区约 {users_per_subcommunity} 个用户")
        
        # 为每个子社区分配用户
        for i in range(self.num_subcommunities):
            start_idx = i * users_per_subcommunity
            if i == self.num_subcommunities - 1:
                # 最后一个子社区包含剩余的所有用户
                end_idx = total_users
            else:
                end_idx = (i + 1) * users_per_subcommunity
            
            subcommunity_users = user_list[start_idx:end_idx]
            
            # 为子社区选择版主
            moderator_count = min(self.moderators_per_subcommunity, len(subcommunity_users))
            moderators = random.sample(subcommunity_users, moderator_count)
            
            # 更新用户信息
            for j, (user_id, user_data) in enumerate(subcommunity_users):
                user_data['subcommunity_id'] = f"subcommunity_{i+1}"
                user_data['is_moderator'] = (user_id, user_data) in moderators
                user_data['user_group'] = "moderator" if user_data['is_moderator'] else "member"
                
                # 为子社区成员设置相似的特质
                self._assign_similar_traits(user_data, i)
                
                users[user_id] = user_data
        
        return users
    
    def _assign_similar_traits(self, user_data: Dict[str, Any], subcommunity_index: int):
        """
        为子社区成员分配相似的特质
        
        Args:
            user_data: 用户数据
            subcommunity_index: 子社区索引
        """
        # 根据子社区索引设置不同的特质倾向
        trait_profiles = [
            # 子社区0: 保守型
            {"O": (0.2, 0.5), "C": (0.6, 0.9), "E": (0.3, 0.6), "A": (0.5, 0.8), "N": (0.2, 0.5)},
            # 子社区1: 开放型
            {"O": (0.7, 0.9), "C": (0.4, 0.7), "E": (0.6, 0.9), "A": (0.4, 0.7), "N": (0.3, 0.6)},
            # 子社区2: 内向型
            {"O": (0.4, 0.7), "C": (0.5, 0.8), "E": (0.2, 0.5), "A": (0.6, 0.9), "N": (0.4, 0.7)},
            # 子社区3: 外向型
            {"O": (0.5, 0.8), "C": (0.3, 0.6), "E": (0.7, 0.9), "A": (0.5, 0.8), "N": (0.2, 0.5)},
            # 子社区4: 平衡型
            {"O": (0.4, 0.6), "C": (0.4, 0.6), "E": (0.4, 0.6), "A": (0.4, 0.6), "N": (0.4, 0.6)},
        ]
        
        # 循环使用特质配置
        profile = trait_profiles[subcommunity_index % len(trait_profiles)]
        
        # 更新用户的人格特质
        for trait, (min_val, max_val) in profile.items():
            user_data["ocean_personality"][trait] = round(random.uniform(min_val, max_val), 2)
        
        # 版主有特殊的认知特质
        if user_data.get('is_moderator', False):
            user_data["cognitive_traits"]["skepticism_score"] = round(random.uniform(0.6, 0.9), 2)
            user_data["cognitive_traits"]["verification_threshold"] = round(random.uniform(0.6, 0.9), 2)
            user_data["cognitive_traits"]["emotional_volatility"] = round(random.uniform(0.2, 0.5), 2)
            user_data["cognitive_traits"]["is_intellectual"] = True
        else:
            user_data["cognitive_traits"]["skepticism_score"] = round(random.uniform(0.2, 0.6), 2)
            user_data["cognitive_traits"]["verification_threshold"] = round(random.uniform(0.2, 0.6), 2)
            user_data["cognitive_traits"]["emotional_volatility"] = round(random.uniform(0.4, 0.8), 2)
            user_data["cognitive_traits"]["is_intellectual"] = False
    
    def build_subcommunity_relationships(self, users: Dict[str, Dict[str, Any]]) -> List[Tuple[str, str]]:
        """
        构建子社区内的关注关系
        
        Args:
            users: 用户字典
            
        Returns:
            关注关系列表 [(follower_id, followee_id), ...]
        """
        logger.info("开始构建子社区关注关系...")
        
        # 按子社区分组用户
        subcommunities = defaultdict(list)
        moderators_by_subcommunity = defaultdict(list)
        
        for user_id, user_data in users.items():
            subcommunity_id = user_data.get('subcommunity_id')
            if subcommunity_id:
                subcommunities[subcommunity_id].append(user_id)
                if user_data.get('is_moderator', False):
                    moderators_by_subcommunity[subcommunity_id].append(user_id)
        
        all_relationships = []
        
        # 为每个子社区构建内部关系
        for subcommunity_id, user_ids in subcommunities.items():
            moderator_ids = moderators_by_subcommunity[subcommunity_id]
            member_ids = [uid for uid in user_ids if uid not in moderator_ids]
            
            logger.info(f"处理 {subcommunity_id}: {len(user_ids)} 个用户 ({len(moderator_ids)} 个版主, {len(member_ids)} 个成员)")
            
            # 1. 成员关注版主
            relationships = self._build_member_to_moderator_relationships(member_ids, moderator_ids)
            all_relationships.extend(relationships)
            
            # 2. 子社区内部成员之间的关注
            relationships = self._build_intra_subcommunity_relationships(user_ids)
            all_relationships.extend(relationships)
        
        # 3. 子社区之间的少量关注
        inter_relationships = self._build_inter_subcommunity_relationships(subcommunities, moderators_by_subcommunity)
        all_relationships.extend(inter_relationships)
        
        logger.info(f"总共生成 {len(all_relationships)} 个关注关系")
        return all_relationships
    
    def _build_member_to_moderator_relationships(self, member_ids: List[str], moderator_ids: List[str]) -> List[Tuple[str, str]]:
        """构建成员关注版主的关系"""
        relationships = []
        
        for member_id in member_ids:
            # 每个成员关注大部分版主
            num_to_follow = max(1, int(len(moderator_ids) * self.member_to_moderator_follow_ratio))
            moderators_to_follow = random.sample(moderator_ids, min(num_to_follow, len(moderator_ids)))
            
            for moderator_id in moderators_to_follow:
                relationships.append((member_id, moderator_id))
        
        return relationships
    
    def _build_intra_subcommunity_relationships(self, user_ids: List[str]) -> List[Tuple[str, str]]:
        """构建子社区内部的关注关系"""
        relationships = []
        
        for user_id in user_ids:
            # 每个用户关注子社区内的一些其他用户
            other_users = [uid for uid in user_ids if uid != user_id]
            if not other_users:
                continue
                
            num_to_follow = max(1, int(len(other_users) * self.intra_subcommunity_follow_ratio))
            users_to_follow = random.sample(other_users, min(num_to_follow, len(other_users)))
            
            for followee_id in users_to_follow:
                relationships.append((user_id, followee_id))
        
        return relationships
    
    def _build_inter_subcommunity_relationships(self, subcommunities: Dict[str, List[str]], 
                                              moderators_by_subcommunity: Dict[str, List[str]]) -> List[Tuple[str, str]]:
        """构建子社区之间的少量关注关系"""
        relationships = []
        
        subcommunity_ids = list(subcommunities.keys())
        
        for i, subcommunity_id in enumerate(subcommunity_ids):
            current_moderators = moderators_by_subcommunity[subcommunity_id]
            
            # 选择其他子社区
            other_subcommunities = [sid for j, sid in enumerate(subcommunity_ids) if i != j]
            
            for moderator_id in current_moderators:
                # 版主可能关注其他子社区的少数版主
                for other_subcommunity_id in other_subcommunities:
                    other_moderators = moderators_by_subcommunity[other_subcommunity_id]
                    
                    if random.random() < self.inter_subcommunity_follow_ratio and other_moderators:
                        target_moderator = random.choice(other_moderators)
                        relationships.append((moderator_id, target_moderator))
        
        return relationships
