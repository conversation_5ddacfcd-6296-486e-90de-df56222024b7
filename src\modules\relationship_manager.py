#!/usr/bin/env python
# -*- coding: utf-8 -*-

import random
import logging
from typing import Dict, Any, List, Tuple
from collections import defaultdict

logger = logging.getLogger(__name__)

class RelationshipManager:
    """
    用户关系管理器
    
    封装不同社区结构的用户关系创建逻辑
    """
    
    def __init__(self, config: Dict[str, Any], agent_module):
        """
        初始化关系管理器
        
        Args:
            config: 配置参数
            agent_module: 代理模块实例
        """
        self.config = config
        self.agent_module = agent_module
        self.community_structure = config.get('community_structure', 'default')
        
    def build_user_relationships(self, users: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        根据配置的社区结构建立用户关系
        
        Args:
            users: 用户字典
            
        Returns:
            包含关系建立结果的字典
        """
        logger.info(f"使用社区结构: {self.community_structure}")
        
        if self.community_structure == 'subcommunity':
            return self._build_subcommunity_structure(users)
        else:
            return self._build_default_structure(users)
    
    def _build_default_structure(self, users: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        构建默认的社区结构（知识分子 + 群众）
        
        Args:
            users: 用户字典
            
        Returns:
            关系建立结果
        """
        logger.info("构建默认社区结构（知识分子 + 群众）")
        
        # 计算知识分子数量
        num_intellectuals = sum(1 for user in users.values()
                               if user["cognitive_traits"].get("is_intellectual", False))
        
        # 获取知识分子和普通群众的ID
        intellectual_ids = [uid for uid, user in users.items()
                           if user["cognitive_traits"].get("is_intellectual", False)]
        regular_user_ids = [uid for uid, user in users.items()
                          if not user["cognitive_traits"].get("is_intellectual", False)]
        
        logger.info(f"找到 {len(intellectual_ids)} 个知识分子，{len(regular_user_ids)} 个普通群众")
        
        all_relationships = []
        results = {}
        
        # 1. 为知识分子建立粉丝关系（普通群众关注知识分子）
        relationships = self._build_intellectual_followers(intellectual_ids, regular_user_ids)
        all_relationships.extend(relationships)
        results['intellectual_followers'] = len(relationships)
        
        # 2. 知识分子关注普通群众
        relationships = self._build_intellectual_follow_regular(intellectual_ids, regular_user_ids)
        all_relationships.extend(relationships)
        results['intellectual_follow_regular'] = len(relationships)
        
        # 3. 普通群众之间建立关注关系
        relationships = self._build_regular_user_relationships(regular_user_ids)
        all_relationships.extend(relationships)
        results['regular_relationships'] = len(relationships)
        
        # 4. 知识分子之间建立关注关系
        relationships = self._build_intellectual_relationships(intellectual_ids)
        all_relationships.extend(relationships)
        results['intellectual_relationships'] = len(relationships)
        
        # 批量执行关注操作
        if all_relationships:
            logger.info(f"开始批量建立 {len(all_relationships)} 个关注关系")
            batch_result = self.agent_module.batch_update_user_follow(all_relationships, follow=True)
            results['batch_result'] = batch_result
            results['total_relationships'] = len(all_relationships)
            
            logger.info(f"默认结构关系建立完成 - 成功: {batch_result['successful_count']}, 失败: {batch_result['failed_count']}")
        
        return results
    
    def _build_subcommunity_structure(self, users: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        构建子社区结构
        
        Args:
            users: 用户字典
            
        Returns:
            关系建立结果
        """
        logger.info("构建子社区结构")
        
        # 导入子社区结构模块
        try:
            from experiment.structure.subcommunity_structure import SubcommunityStructure
        except ImportError as e:
            logger.error(f"无法导入子社区结构模块: {e}")
            logger.info("回退到默认结构")
            return self._build_default_structure(users)
        
        # 创建子社区结构实例
        subcommunity_config = self.config.get('subcommunity_config', {})
        subcommunity_manager = SubcommunityStructure(subcommunity_config)
        
        # 分配用户到子社区
        updated_users = subcommunity_manager.assign_users_to_subcommunities(users)
        
        # 更新用户信息到数据库
        self._update_users_in_database(updated_users)
        
        # 构建子社区关系
        all_relationships = subcommunity_manager.build_subcommunity_relationships(updated_users)
        
        # 批量执行关注操作
        results = {}
        if all_relationships:
            logger.info(f"开始批量建立 {len(all_relationships)} 个子社区关注关系")
            batch_result = self.agent_module.batch_update_user_follow(all_relationships, follow=True)
            results['batch_result'] = batch_result
            results['total_relationships'] = len(all_relationships)
            
            logger.info(f"子社区结构关系建立完成 - 成功: {batch_result['successful_count']}, 失败: {batch_result['failed_count']}")
        
        # 统计子社区信息
        subcommunity_stats = self._get_subcommunity_stats(updated_users)
        results['subcommunity_stats'] = subcommunity_stats
        
        return results
    
    def _build_intellectual_followers(self, intellectual_ids: List[str], regular_user_ids: List[str]) -> List[Tuple[str, str]]:
        """为知识分子建立粉丝关系"""
        relationships = []
        
        for intellectual_id in intellectual_ids:
            follow_ratio = random.uniform(
                self.config['regular_to_intellectual_follow_ratio_min'],
                self.config['regular_to_intellectual_follow_ratio_max']
            )
            num_followers = max(1, int(len(regular_user_ids) * follow_ratio))
            followers = random.sample(regular_user_ids, min(num_followers, len(regular_user_ids)))
            
            for follower_id in followers:
                relationships.append((follower_id, intellectual_id))
        
        return relationships
    
    def _build_intellectual_follow_regular(self, intellectual_ids: List[str], regular_user_ids: List[str]) -> List[Tuple[str, str]]:
        """知识分子关注普通群众"""
        relationships = []
        
        for intellectual_id in intellectual_ids:
            follow_ratio = random.uniform(
                self.config['intellectual_to_regular_follow_ratio_min'],
                self.config['intellectual_to_regular_follow_ratio_max']
            )
            num_to_follow = max(1, int(len(regular_user_ids) * follow_ratio))
            followees = random.sample(regular_user_ids, min(num_to_follow, len(regular_user_ids)))
            
            for followee_id in followees:
                relationships.append((intellectual_id, followee_id))
        
        return relationships
    
    def _build_regular_user_relationships(self, regular_user_ids: List[str]) -> List[Tuple[str, str]]:
        """普通群众之间建立关注关系"""
        relationships = []
        
        if len(regular_user_ids) < 2:
            return relationships
        
        total_users = len(regular_user_ids)
        mean_followers = total_users * self.config['regular_follow_mean_ratio']
        std_followers = total_users * self.config['regular_follow_std_ratio']
        max_followers = int(total_users * self.config['regular_follow_max_ratio'])
        
        for user_id in regular_user_ids:
            follower_count = random.normalvariate(mean_followers, std_followers)
            follower_count = max(0, min(max_followers, int(round(follower_count))))
            
            if follower_count == 0:
                continue
            
            potential_followers = [uid for uid in regular_user_ids if uid != user_id]
            if not potential_followers:
                continue
            
            actual_follower_count = min(follower_count, len(potential_followers))
            if actual_follower_count > 0:
                followers = random.sample(potential_followers, actual_follower_count)
                for follower_id in followers:
                    relationships.append((follower_id, user_id))
        
        return relationships
    
    def _build_intellectual_relationships(self, intellectual_ids: List[str]) -> List[Tuple[str, str]]:
        """知识分子之间建立关注关系"""
        relationships = []
        
        for i, intellectual_id in enumerate(intellectual_ids):
            other_intellectuals = [uid for j, uid in enumerate(intellectual_ids) if i != j]
            if not other_intellectuals:
                continue
            
            follow_ratio = random.uniform(
                self.config['intellectual_to_intellectual_follow_ratio_min'],
                self.config['intellectual_to_intellectual_follow_ratio_max']
            )
            num_to_follow = max(1, int(len(other_intellectuals) * follow_ratio))
            followees = random.sample(other_intellectuals, min(num_to_follow, len(other_intellectuals)))
            
            for followee_id in followees:
                relationships.append((intellectual_id, followee_id))
        
        return relationships
    
    def _update_users_in_database(self, users: Dict[str, Dict[str, Any]]):
        """更新用户信息到数据库"""
        logger.info("更新用户子社区信息到数据库...")
        
        # 这里可以批量更新用户信息
        # 暂时跳过，因为需要访问ES客户端
        pass
    
    def _get_subcommunity_stats(self, users: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """获取子社区统计信息"""
        stats = defaultdict(lambda: {'total': 0, 'moderators': 0, 'members': 0})
        
        for user_data in users.values():
            subcommunity_id = user_data.get('subcommunity_id')
            if subcommunity_id:
                stats[subcommunity_id]['total'] += 1
                if user_data.get('is_moderator', False):
                    stats[subcommunity_id]['moderators'] += 1
                else:
                    stats[subcommunity_id]['members'] += 1
        
        return dict(stats)
