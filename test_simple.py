#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单测试脚本
"""

print("开始测试...")

try:
    from config.config import Config
    print("✓ 成功导入配置模块")
    
    config = Config.get_simulation_config()
    print(f"✓ 获取配置成功")
    print(f"  社区结构: {config.get('community_structure', 'default')}")
    print(f"  用户数量: {config.get('num_users', 100)}")
    
    subcommunity_config = config.get('subcommunity_config', {})
    print(f"  子社区配置: {subcommunity_config}")
    
    print("✓ 配置测试完成")
    
except Exception as e:
    print(f"✗ 配置测试失败: {e}")
    import traceback
    traceback.print_exc()

try:
    import experiment.structure.subcommunity_structure
    print("✓ 成功导入子社区结构模块")
except Exception as e:
    print(f"✗ 子社区结构模块导入失败: {e}")

try:
    import src.modules.relationship_manager
    print("✓ 成功导入关系管理器模块")
except Exception as e:
    print(f"✗ 关系管理器模块导入失败: {e}")

print("测试完成!")
