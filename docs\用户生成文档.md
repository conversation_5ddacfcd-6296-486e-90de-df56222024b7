

# 人格化内容生成策略：修辞基元激活与选择模型

## 1. 系统愿景与目标

本模型旨在取代过去为不同人格绑定固定“战术”的僵化模式，建立一个动态、量化、且更符合心理学常识的内容生成策略。其核心目标是：

1.  **人格的整体性**：智能体的每一次发言，都应是其大五人格（OCEAN）五个维度分数**共同作用**的结果，而非由单一高分特质主导。
2.  **量化决策**：将“倾向于”、“抑制”等模糊描述，转化为一个清晰、可计算的**概率模型**，决定在特定情境下使用何种论证方式。
3.  **策略的灵活性**：智能体的行为应具有合理的**随机性与多样性**，避免在相同情境下总是做出完全相同的反应。
4.  **高度可配置**：模型中的核心参数（如影响矩阵、温度参数）应易于调整，以便对智能体群体的宏观行为进行调试和校准。

该模型通过一个四步流程，将智能体的连续人格分数，映射到一组离散的“修辞基元”选择概率上，从而指导后续的图遍历和内容生成。

## 2. 核心组件：修辞基元的图遍历算法



| 修辞基元 (Primitive) | 核心目的 | 搜索策略 | 关键参数与执行逻辑 |
| :--- | :--- | :--- | :--- |
| **`FindStrongestSupport`** | 建立**深度**的逻辑主干 | **贪婪深度优先 (Greedy DFS)** | **目标**: 找到一条高质量的、层层递进的论证链。<br>**逻辑**: <br>1. 从起点信念 `A` 出发。<br>2. 在所有出向的`support`类边中，选择**权重最高**的一条，移动到下一个节点 `B`。<br>3. 从 `B` 继续重复此过程。<br>4. 直到达到`max_depth`或当前节点没有符合`min_weight`的出向`support`边为止。<br>**参数**: <br>- `max_depth` (最大深度): **2-3 hops**。超过3层的论证在单次发言中会显得冗长。<br>- `min_weight` (最小权重): **~0.5**。过滤掉弱支撑，保证逻辑链的强度。 |
| **`FindEvidenceCluster`** | 提供**广度**的事实/案例支撑 | **广度优先 (BFS)** | **目标**: 收集多个并列的、直接的证据来支撑观点。<br>**逻辑**: <br>1. 从起点信念 `A` 出发。<br>2. 收集所有与 `A` 通过`is_evidence_for`或`is_example_of`边直接相连的邻居节点。<br>3. 如果节点数量超过 `max_nodes`，则根据**边的权重**进行降序排序，选取Top N个。<br>**参数**: <br>- `max_hops` (最大跳数): **严格为 1**。只寻找最直接的证据。<br>- `max_nodes` (最大节点数): **3-5个**。提供足够的证据数量，同时避免信息过载。 |
| **`IdentifyContradiction`**| 识别**最主要**的辩论目标 | **1跳邻居查询** | **目标**: 快速锁定最强的对立观点，集中火力。<br>**逻辑**: <br>1. 从起点信念 `A` 出发。<br>2. 查找所有通过`contradict`类边相连的1跳邻居。<br>3. 在这些邻居中，选择**边权重最高**的那个作为主要对立观点 `B`。<br>**参数**: <br>- `max_hops`: **严格为 1**。<br>- `selection_logic`: `max(edge_weight)`。 |
| **`DismantleSupport`** | 拆解对方论证的**核心支柱** | **两步查询** | **目标**: 打击对方论点的根基，而非表面。<br>**逻辑**: <br>1. **(步骤一)** 调用 `IdentifyContradiction` 算法，找到主要对立观点 `B`。<br>2. **(步骤二)** 从 `B` 出发，查询所有**入向**的`support`类边，选择**权重最高**的那条边。该边的源头节点 `C` 即为要攻击的核心支柱。<br>**参数**: <br>- 无独立参数，组合调用其他基元的逻辑。 |
| **`TraceNegativeCascade`**| 推演风险的**连锁反应** | **带条件的贪婪深度优先** | **目标**: 描绘一个由小风险引发大灾难的、令人警惕的场景。<br>**逻辑**: <br>1. 从起点议题 `A` 出发。<br>2. 在所有出向的`causes`或`imply`边中，选择权重最高且**指向一个情感倾向为负面 (`emotional_disposition`) 的信念**的边，移动到下一个节点 `B`。<br>3. 重复此过程，直到达到`max_depth`或找不到符合条件的下一跳。<br>**参数**: <br>- `max_depth`: **2-3 hops**。足以展示多米诺骨牌效应。<br>- `node_filter`: `node.emotional_disposition` 必须为负面。 |
| **`SeekCommonGround`** | 寻找双方的**共同上层价值** | **双向向上BFS** | **目标**: 化解冲突，找到双方都认可的更高层原则。<br>**逻辑**: <br>1. 确定我方观点 `A` 和对立观点 `B`。<br>2. 从 `A` 开始，向上（沿**入向**的`is_example_of`等边）进行BFS，记录所有访问过的祖先节点及其深度，存入 `ancestors_A`。限制深度为`max_search_depth`。<br>3. 对 `B` 重复此过程，存入 `ancestors_B`。<br>4. 计算 `ancestors_A` 和 `ancestors_B` 的交集。如果交集不为空，选择其中**深度最小（最接近A和B）**的那个节点作为共同点`C`。<br>**参数**: <br>- `max_search_depth`: **2-3 hops**。向上寻找不宜过深，否则概念会过于抽象。 |
| **`StateSimpleFacts`** | 重申**简单、高确定性**的核心看法 | **带双重过滤的BFS** | **目标**: 拒绝复杂性，用一系列不容置疑的“事实”淹没对方。<br>**逻辑**: <br>1. 从起点信念 `A` 出发。<br>2. 进行1跳BFS，收集所有通过`support`类边相连的邻居节点。<br>3. 对收集到的邻居节点进行**双重过滤**：<br>    a. **边权重过滤**: 边的权重必须高于 `min_edge_weight`。<br>    b. **节点置信度过滤**: 邻居节点本身的置信度 (`BELIEF-C`) 必须高于 `min_belief_confidence`。<br>4. 返回通过双重过滤的 `max_nodes` 个节点。<br>**参数**: <br>- `max_hops`: **严格为 1**。<br>- `min_edge_weight`: **~0.6**。只接受强支撑。<br>- `min_belief_confidence`: **~0.7**。只使用自己深信不疑的论据。<br>- `max_nodes`: **2-4个**。通过重复来加强语气。 |

---



### 2.2 人格-基元影响矩阵 (PIM)

这是模型的核心规则库，定义了每一种人格特质对每一种修辞基元的**促进（正值）**或**抑制（负值）**程度。该矩阵是系统的“出厂设置”，其数值基于心理学常识进行标定，范围为-1到1。

**PIM (Personality-to-Primitive Influence Matrix) V1.0**

| 修辞基元 (Primitive) | 开放性 (O) | 尽责性 (C) | 外向性 (E) | 宜人性 (A) | 神经质 (N) |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `FindStrongestSupport` | +0.2 | **+0.8** | +0.1 | +0.1 | +0.1 |
| `FindEvidenceCluster` | **+0.7** | +0.4 | **+0.5** | +0.2 | -0.2 |
| `IdentifyContradiction`| +0.1 | +0.3 | +0.2 | **-0.8** | **+0.6** |
| `DismantleSupport` | +0.1 | **+0.7** | +0.1 | **-0.9** | +0.4 |
| `TraceNegativeCascade`| -0.3 | +0.2 | -0.4 | -0.5 | **+0.9** |
| `SeekCommonGround` | **+0.6** | +0.2 | **+0.4** | **+0.9** | **-0.7** |
| `StateSimpleFacts` | **-0.8** | -0.5 | -0.2 | +0.3 | -0.1 |

## 3. 算法工作流程

### 步骤一：计算基础激活分 (Base Activation Score)

对于给定的智能体，其每个基元的激活分是其**大五人格分数向量（0-1范围）**与PIM矩阵中**对应基元行向量**的点积。

**公式**:
```
ActivationScore(Primitive_i) = Σ (Agent.PersonalityScore_j * PIM_ij)
```
*   `i` 代表某个修辞基元。
*   `j` 遍历大五人格的五个维度 (O, C, E, A, N)。

**示例演算**:
假设智能体人格为：`{O: 0.5, C: 0.8, E: 0.5, A: 0.5, N: 0.2}`。

*   **`DismantleSupport` 的激活分**:
    `(0.5*0.1) + (0.8*0.7) + (0.5*0.1) + (0.5*-0.9) + (0.2*0.4)`
    `= 0.05 + 0.56 + 0.05 - 0.45 + 0.08 = 0.29`

*   **`SeekCommonGround` 的激活分**:
    `(0.5*0.6) + (0.8*0.2) + (0.5*0.4) + (0.5*0.9) + (0.2*-0.7)`
    `= 0.30 + 0.16 + 0.20 + 0.45 - 0.14 = 0.97`

*   ...对所有7个基元重复此计算...

### 步骤二：应用Softmax进行概率化

为了将任意范围的激活分转化为总和为1的概率分布，并有效解决“所有概率都过低”的问题，我们使用带**温度参数（τ）**的Softmax函数。

**公式**:
```
Probability(Primitive_i) = e^(ActivationScore_i * τ) / Σ_k(e^(ActivationScore_k * τ))
```

**温度参数 (τ, Tau) 的关键作用与建议值**:
`τ` 控制着概率分布的“尖锐程度”，是防止概率过低、决策过于随机的关键。

*   **`τ = 1`**: 标准Softmax，可能导致概率分布过于平缓，选择区分度不大。
*   **`τ > 1` (推荐)**: 增强了高分选项的优势，使得智能体的选择更加果断和有倾向性。
    *   **建议起始值**: `τ` **可以设置在 `2.0` 到 `4.0` 之间**。这个范围能够有效地将激活分的差距放大，使得得分最高的基元获得显著高的概率，同时其他基元也有较小的机会被选中。
*   **动态 `τ` (高级)**: `τ` 值本身可以与人格挂钩。例如，一个高尽责性、低开放性的智能体，其决策可能更具确定性，可以使用更高的`τ`值（如4.0）；而一个高开放性的智能体，其行为更具探索性，可以使用更低的`τ`值（如2.0）。

**示例演算 (续)**:
假设 τ = 2.5, `DismantleSupport`的最终概率将是 `e^(0.29 * 2.5)` 除以所有项的指数和。而`SeekCommonGround`的概率分子将是`e^(0.97 * 2.5)`，由于其指数远大于前者，它的最终概率会显著更高。

### 步骤三：加权随机选择与策略组合

得到概率分布后，系统通过加权随机抽样（如同一个带偏向的轮盘赌）来决定本次发言的策略。

1.  如果是发帖，则随机选取一个信念作为初始立场，之后再选取基元。如果是评论或者回复，则根据浏览内容，选取最相似的信念作为初始立场，之后再选取基元。

2.  **基元策略选择 (高级)**:
    可以设计一个抽取多次的机制，形成更复杂的策略。例如：
    每个基元根据输出的概率进行抽取

### 步骤四：生成话语指令包并交付LLM

算法的最终输出不是自然语言，而是一个结构化的“话语指令包”，清晰地告诉LLM应该如何组织语言。



## 4. 实施建议与参数调优

*   **PIM矩阵的迭代**: PIM矩阵是本模型的核心假设。在系统运行初期，应收集和分析生成内容与预期人格的匹配度，并对矩阵中的权重进行微调。
*   **情境适应性**: 在未来的版本中，可以引入“情境向量”来动态调整基元的激活分。例如，如果智能体正在被人激烈反驳，所有“反驳类”基元（如`DismantleSupport`）的基础激活分可以获得一个临时的“上下文加成”，使其更有可能进行回击。
*   **温度参数的实验**: 针对不同的社区环境和模拟目标，对`τ`值进行实验，找到能产生最理想群体行为的范围。一个过于“狂热”（高τ）或过于“随机”（低τ）的社区可能都不是理想状态。